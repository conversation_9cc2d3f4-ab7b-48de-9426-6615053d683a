/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/drama/[id]"],{

/***/ "__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ArrowLeft: function() { return /* reexport safe */ _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }
/* harmony export */ });
/* harmony import */ var _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-left.js */ "./node_modules/lucide-react/dist/esm/icons/arrow-left.js");



/***/ }),

/***/ "__barrel_optimize__?names=Check!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**********************************************************************************************!*\
  !*** __barrel_optimize__?names=Check!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**********************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Check: function() { return /* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }
/* harmony export */ });
/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ "./node_modules/lucide-react/dist/esm/icons/check.js");



/***/ }),

/***/ "__barrel_optimize__?names=Check,Settings,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,Settings,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: function() { return /* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Settings: function() { return /* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   Zap: function() { return /* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/zap.js */ \"./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxTZXR0aW5ncyxaYXAhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDbUQ7QUFDTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9kZDUxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVjayB9IGZyb20gXCIuL2ljb25zL2NoZWNrLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2V0dGluZ3MgfSBmcm9tIFwiLi9pY29ucy9zZXR0aW5ncy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFphcCB9IGZyb20gXCIuL2ljb25zL3phcC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,Settings,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=Loader,Maximize,Minimize,Play,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Loader,Maximize,Minimize,Play,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loader: function() { return /* reexport safe */ _icons_loader_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Maximize: function() { return /* reexport safe */ _icons_maximize_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   Minimize: function() { return /* reexport safe */ _icons_minimize_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   Play: function() { return /* reexport safe */ _icons_play_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   Settings: function() { return /* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_loader_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/loader.js */ \"./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _icons_maximize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/maximize.js */ \"./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _icons_minimize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/minimize.js */ \"./node_modules/lucide-react/dist/esm/icons/minimize.js\");\n/* harmony import */ var _icons_play_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/play.js */ \"./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Mb2FkZXIsTWF4aW1pemUsTWluaW1pemUsUGxheSxTZXR0aW5ncyE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDcUQ7QUFDSTtBQUNBO0FBQ1IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/NzBiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9hZGVyIH0gZnJvbSBcIi4vaWNvbnMvbG9hZGVyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWF4aW1pemUgfSBmcm9tIFwiLi9pY29ucy9tYXhpbWl6ZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1pbmltaXplIH0gZnJvbSBcIi4vaWNvbnMvbWluaW1pemUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbGF5IH0gZnJvbSBcIi4vaWNvbnMvcGxheS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNldHRpbmdzIH0gZnJvbSBcIi4vaWNvbnMvc2V0dGluZ3MuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Loader,Maximize,Minimize,Play,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=Pause,Play,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Pause,Play,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pause: function() { return /* reexport safe */ _icons_pause_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Play: function() { return /* reexport safe */ _icons_play_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   SkipBack: function() { return /* reexport safe */ _icons_skip_back_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   SkipForward: function() { return /* reexport safe */ _icons_skip_forward_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_pause_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/pause.js */ \"./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _icons_play_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/play.js */ \"./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _icons_skip_back_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/skip-back.js */ \"./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var _icons_skip_forward_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/skip-forward.js */ \"./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1QYXVzZSxQbGF5LFNraXBCYWNrLFNraXBGb3J3YXJkIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDbUQ7QUFDRjtBQUNTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzM1NjgiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBhdXNlIH0gZnJvbSBcIi4vaWNvbnMvcGF1c2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbGF5IH0gZnJvbSBcIi4vaWNvbnMvcGxheS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNraXBCYWNrIH0gZnJvbSBcIi4vaWNvbnMvc2tpcC1iYWNrLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2tpcEZvcndhcmQgfSBmcm9tIFwiLi9pY29ucy9za2lwLWZvcndhcmQuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Pause,Play,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=Volume1,Volume2,VolumeX!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Volume1,Volume2,VolumeX!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Volume1: function() { return /* reexport safe */ _icons_volume_1_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Volume2: function() { return /* reexport safe */ _icons_volume_2_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   VolumeX: function() { return /* reexport safe */ _icons_volume_x_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_volume_1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/volume-1.js */ \"./node_modules/lucide-react/dist/esm/icons/volume-1.js\");\n/* harmony import */ var _icons_volume_2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/volume-2.js */ \"./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _icons_volume_x_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/volume-x.js */ \"./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Wb2x1bWUxLFZvbHVtZTIsVm9sdW1lWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN3RDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2YxOTkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZvbHVtZTEgfSBmcm9tIFwiLi9pY29ucy92b2x1bWUtMS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZvbHVtZTIgfSBmcm9tIFwiLi9pY29ucy92b2x1bWUtMi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZvbHVtZVggfSBmcm9tIFwiLi9pY29ucy92b2x1bWUteC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Volume1,Volume2,VolumeX!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCursor%20Project%5C%E7%9F%AD%E5%89%A7%5Csrc%5Cpages%5Cdrama%5C%5Bid%5D.tsx&page=%2Fdrama%2F%5Bid%5D!":
/*!***************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCursor%20Project%5C%E7%9F%AD%E5%89%A7%5Csrc%5Cpages%5Cdrama%5C%5Bid%5D.tsx&page=%2Fdrama%2F%5Bid%5D! ***!
  \***************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/drama/[id]\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/drama/[id].tsx */ \"./src/pages/drama/[id].tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/drama/[id]\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDQ3Vyc29yJTIwUHJvamVjdCU1QyVFNyU5RiVBRCVFNSU4OSVBNyU1Q3NyYyU1Q3BhZ2VzJTVDZHJhbWElNUMlNUJpZCU1RC50c3gmcGFnZT0lMkZkcmFtYSUyRiU1QmlkJTVEISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDhEQUE0QjtBQUNuRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NWNjYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2RyYW1hL1tpZF1cIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9kcmFtYS9baWRdLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvZHJhbWEvW2lkXVwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCursor%20Project%5C%E7%9F%AD%E5%89%A7%5Csrc%5Cpages%5Cdrama%5C%5Bid%5D.tsx&page=%2Fdrama%2F%5Bid%5D!\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/arrow-left.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-left.js ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArrowLeft; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst ArrowLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowLeft\", [\n    [\n        \"path\",\n        {\n            d: \"m12 19-7-7 7-7\",\n            key: \"1l729n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 12H5\",\n            key: \"x3x0zl\"\n        }\n    ]\n]);\n //# sourceMappingURL=arrow-left.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2Fycm93LWxlZnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxNQUFBQSxZQUFZQyxnRUFBZ0JBLENBQUMsYUFBYTtJQUM5QztRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUFrQkMsS0FBSztRQUFBO0tBQVU7SUFDL0M7UUFBQztRQUFRO1lBQUVELEdBQUc7WUFBWUMsS0FBSztRQUFBO0tBQVU7Q0FDMUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9pY29ucy9hcnJvdy1sZWZ0LnRzPzRiNTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBBcnJvd0xlZnRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1USWdNVGt0TnkwM0lEY3ROeUlnTHo0S0lDQThjR0YwYUNCa1BTSk5NVGtnTVRKSU5TSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9hcnJvdy1sZWZ0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQXJyb3dMZWZ0ID0gY3JlYXRlTHVjaWRlSWNvbignQXJyb3dMZWZ0JywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdtMTIgMTktNy03IDctNycsIGtleTogJzFsNzI5bicgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xOSAxMkg1Jywga2V5OiAneDN4MHpsJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBBcnJvd0xlZnQ7XG4iXSwibmFtZXMiOlsiQXJyb3dMZWZ0IiwiY3JlYXRlTHVjaWRlSWNvbiIsImQiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/arrow-left.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Check; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Check\", [\n    [\n        \"polyline\",\n        {\n            points: \"20 6 9 17 4 12\",\n            key: \"10jjfj\"\n        }\n    ]\n]);\n //# sourceMappingURL=check.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sTUFBQUEsUUFBUUMsZ0VBQWdCQSxDQUFDLFNBQVM7SUFDdEM7UUFBQztRQUFZO1lBQUVDLFFBQVE7WUFBa0JDLEtBQUs7UUFBQTtLQUFVO0NBQ3pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvaWNvbnMvY2hlY2sudHM/NzYxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZWNrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXlNQ0EySURrZ01UY2dOQ0F4TWlJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZWNrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hlY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdDaGVjaycsIFtcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMjAgNiA5IDE3IDQgMTInLCBrZXk6ICcxMGpqZmonIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZWNrO1xuIl0sIm5hbWVzIjpbIkNoZWNrIiwiY3JlYXRlTHVjaWRlSWNvbiIsInBvaW50cyIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/loader.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader.js ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Loader; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Loader = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Loader\", [\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"2\",\n            y2: \"6\",\n            key: \"gza1u7\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"18\",\n            y2: \"22\",\n            key: \"1qhbu9\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4.93\",\n            x2: \"7.76\",\n            y1: \"4.93\",\n            y2: \"7.76\",\n            key: \"xae44r\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16.24\",\n            x2: \"19.07\",\n            y1: \"16.24\",\n            y2: \"19.07\",\n            key: \"bxnmvf\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"2\",\n            x2: \"6\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"89khin\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"18\",\n            x2: \"22\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"pb8tfm\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4.93\",\n            x2: \"7.76\",\n            y1: \"19.07\",\n            y2: \"16.24\",\n            key: \"1uxjnu\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16.24\",\n            x2: \"19.07\",\n            y1: \"7.76\",\n            y2: \"4.93\",\n            key: \"6duxfx\"\n        }\n    ]\n]);\n //# sourceMappingURL=loader.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/loader.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/maximize.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/maximize.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Maximize; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Maximize = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Maximize\", [\n    [\n        \"path\",\n        {\n            d: \"M8 3H5a2 2 0 0 0-2 2v3\",\n            key: \"1dcmit\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 8V5a2 2 0 0 0-2-2h-3\",\n            key: \"1e4gt3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 16v3a2 2 0 0 0 2 2h3\",\n            key: \"wsl5sc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 21h3a2 2 0 0 0 2-2v-3\",\n            key: \"18trek\"\n        }\n    ]\n]);\n //# sourceMappingURL=maximize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/maximize.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/minimize.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/minimize.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Minimize; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Minimize = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Minimize\", [\n    [\n        \"path\",\n        {\n            d: \"M8 3v3a2 2 0 0 1-2 2H3\",\n            key: \"hohbtr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 8h-3a2 2 0 0 1-2-2V3\",\n            key: \"5jw1f3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 16h3a2 2 0 0 1 2 2v3\",\n            key: \"198tvr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 21v-3a2 2 0 0 1 2-2h3\",\n            key: \"ph8mxp\"\n        }\n    ]\n]);\n //# sourceMappingURL=minimize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/minimize.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/pause.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/pause.js ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Pause; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Pause = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Pause\", [\n    [\n        \"rect\",\n        {\n            width: \"4\",\n            height: \"16\",\n            x: \"6\",\n            y: \"4\",\n            key: \"iffhe4\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"4\",\n            height: \"16\",\n            x: \"14\",\n            y: \"4\",\n            key: \"sjin7j\"\n        }\n    ]\n]);\n //# sourceMappingURL=pause.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/pause.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/play.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/play.js ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Play; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Play = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Play\", [\n    [\n        \"polygon\",\n        {\n            points: \"5 3 19 12 5 21 5 3\",\n            key: \"191637\"\n        }\n    ]\n]);\n //# sourceMappingURL=play.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3BsYXkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxNQUFBQSxPQUFPQyxnRUFBZ0JBLENBQUMsUUFBUTtJQUNwQztRQUFDO1FBQVc7WUFBRUMsUUFBUTtZQUFzQkMsS0FBSztRQUFBO0tBQVU7Q0FDNUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9pY29ucy9wbGF5LnRzP2QxMjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBQbGF5XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVdkdmJpQndiMmx1ZEhNOUlqVWdNeUF4T1NBeE1pQTFJREl4SURVZ015SWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9wbGF5XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUGxheSA9IGNyZWF0ZUx1Y2lkZUljb24oJ1BsYXknLCBbXG4gIFsncG9seWdvbicsIHsgcG9pbnRzOiAnNSAzIDE5IDEyIDUgMjEgNSAzJywga2V5OiAnMTkxNjM3JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBQbGF5O1xuIl0sIm5hbWVzIjpbIlBsYXkiLCJjcmVhdGVMdWNpZGVJY29uIiwicG9pbnRzIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/play.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/skip-back.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/skip-back.js ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SkipBack; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst SkipBack = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"SkipBack\", [\n    [\n        \"polygon\",\n        {\n            points: \"19 20 9 12 19 4 19 20\",\n            key: \"o2sva\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"5\",\n            x2: \"5\",\n            y1: \"19\",\n            y2: \"5\",\n            key: \"1ocqjk\"\n        }\n    ]\n]);\n //# sourceMappingURL=skip-back.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/skip-back.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/skip-forward.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/skip-forward.js ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SkipForward; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst SkipForward = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"SkipForward\", [\n    [\n        \"polygon\",\n        {\n            points: \"5 4 15 12 5 20 5 4\",\n            key: \"16p6eg\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"19\",\n            x2: \"19\",\n            y1: \"5\",\n            y2: \"19\",\n            key: \"futhcm\"\n        }\n    ]\n]);\n //# sourceMappingURL=skip-forward.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/skip-forward.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/volume-1.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-1.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Volume1; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Volume1 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Volume1\", [\n    [\n        \"polygon\",\n        {\n            points: \"11 5 6 9 2 9 2 15 6 15 11 19 11 5\",\n            key: \"16drj5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15.54 8.46a5 5 0 0 1 0 7.07\",\n            key: \"ltjumu\"\n        }\n    ]\n]);\n //# sourceMappingURL=volume-1.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/volume-1.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/volume-2.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-2.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Volume2; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Volume2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Volume2\", [\n    [\n        \"polygon\",\n        {\n            points: \"11 5 6 9 2 9 2 15 6 15 11 19 11 5\",\n            key: \"16drj5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15.54 8.46a5 5 0 0 1 0 7.07\",\n            key: \"ltjumu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.07 4.93a10 10 0 0 1 0 14.14\",\n            key: \"1kegas\"\n        }\n    ]\n]);\n //# sourceMappingURL=volume-2.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/volume-2.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/volume-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-x.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VolumeX; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst VolumeX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"VolumeX\", [\n    [\n        \"polygon\",\n        {\n            points: \"11 5 6 9 2 9 2 15 6 15 11 19 11 5\",\n            key: \"16drj5\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"22\",\n            x2: \"16\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"1ewh16\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"22\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"5ykzw1\"\n        }\n    ]\n]);\n //# sourceMappingURL=volume-x.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/volume-x.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Zap; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Zap\", [\n    [\n        \"polygon\",\n        {\n            points: \"13 2 3 14 12 14 11 22 21 10 12 10 13 2\",\n            key: \"45s27k\"\n        }\n    ]\n]);\n //# sourceMappingURL=zap.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3phcC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWFNLE1BQUFBLE1BQU1DLGdFQUFnQkEsQ0FBQyxPQUFPO0lBQ2xDO1FBQ0U7UUFDQTtZQUFFQyxRQUFRO1lBQTBDQyxLQUFLO1FBQVM7S0FDcEU7Q0FDRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2ljb25zL3phcC50cz9jMmFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgWmFwXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVdkdmJpQndiMmx1ZEhNOUlqRXpJRElnTXlBeE5DQXhNaUF4TkNBeE1TQXlNaUF5TVNBeE1DQXhNaUF4TUNBeE15QXlJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy96YXBcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBaYXAgPSBjcmVhdGVMdWNpZGVJY29uKCdaYXAnLCBbXG4gIFtcbiAgICAncG9seWdvbicsXG4gICAgeyBwb2ludHM6ICcxMyAyIDMgMTQgMTIgMTQgMTEgMjIgMjEgMTAgMTIgMTAgMTMgMicsIGtleTogJzQ1czI3aycgfSxcbiAgXSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBaYXA7XG4iXSwibmFtZXMiOlsiWmFwIiwiY3JlYXRlTHVjaWRlSWNvbiIsInBvaW50cyIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "./src/components/player/PlayerControls.tsx":
/*!**************************************************!*\
  !*** ./src/components/player/PlayerControls.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlayerControls: function() { return /* binding */ PlayerControls; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Pause_Play_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Pause,Play,SkipBack,SkipForward!=!lucide-react */ \"__barrel_optimize__?names=Pause,Play,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\n\n\n\n\n/**\n * 播放器控制按钮组件\n */ function PlayerControls({ isPlaying, onPlayPause, onRewind, onFastForward, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: onRewind,\n                className: \"p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors\",\n                \"aria-label\": \"快退10秒\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pause_Play_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_4__.SkipBack, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: onPlayPause,\n                className: \"p-3 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors\",\n                \"aria-label\": isPlaying ? \"暂停\" : \"播放\",\n                children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pause_Play_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Pause, {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pause_Play_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Play, {\n                    className: \"w-6 h-6 ml-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: onFastForward,\n                className: \"p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors\",\n                \"aria-label\": \"快进10秒\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pause_Play_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_4__.SkipForward, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\PlayerControls.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = PlayerControls;\nvar _c;\n$RefreshReg$(_c, \"PlayerControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/player/PlayerControls.tsx\n"));

/***/ }),

/***/ "./src/components/player/ProgressBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/player/ProgressBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressBar: function() { return /* binding */ ProgressBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n/**\n * 视频进度条组件\n */ function ProgressBar({ currentTime, duration, bufferedTime = 0, onSeek, className }) {\n    _s();\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoverTime, setHoverTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoverPosition, setHoverPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const progressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /**\n   * 计算进度百分比\n   */ const progressPercentage = duration > 0 ? currentTime / duration * 100 : 0;\n    const bufferedPercentage = duration > 0 ? bufferedTime / duration * 100 : 0;\n    /**\n   * 获取时间位置\n   */ const getTimeFromPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((clientX)=>{\n        if (!progressRef.current) return 0;\n        const rect = progressRef.current.getBoundingClientRect();\n        const position = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));\n        return position * duration;\n    }, [\n        duration\n    ]);\n    /**\n   * 处理鼠标按下\n   */ const handleMouseDown = (event)=>{\n        setIsDragging(true);\n        const time = getTimeFromPosition(event.clientX);\n        onSeek(time);\n    };\n    /**\n   * 处理鼠标移动\n   */ const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        if (!progressRef.current) return;\n        const rect = progressRef.current.getBoundingClientRect();\n        const position = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));\n        const time = position * duration;\n        setHoverTime(time);\n        setHoverPosition(position * 100);\n        if (isDragging) {\n            onSeek(time);\n        }\n    }, [\n        duration,\n        isDragging,\n        onSeek\n    ]);\n    /**\n   * 处理鼠标抬起\n   */ const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(false);\n    }, []);\n    /**\n   * 处理鼠标离开\n   */ const handleMouseLeave = ()=>{\n        setHoverTime(null);\n    };\n    /**\n   * 格式化时间显示\n   */ const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    /**\n   * 监听全局鼠标事件\n   */ react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative group\", className),\n        children: [\n            hoverTime !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: 10\n                },\n                className: \"absolute bottom-full mb-2 px-2 py-1 bg-black bg-opacity-80 text-white text-xs rounded pointer-events-none\",\n                style: {\n                    left: `${hoverPosition}%`,\n                    transform: \"translateX(-50%)\"\n                },\n                children: formatTime(hoverTime)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: progressRef,\n                className: \"relative h-1 bg-white bg-opacity-30 rounded-full cursor-pointer group-hover:h-2 transition-all duration-200\",\n                onMouseDown: handleMouseDown,\n                onMouseMove: (e)=>handleMouseMove(e.nativeEvent),\n                onMouseLeave: handleMouseLeave,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 h-full bg-white bg-opacity-50 rounded-full transition-all duration-200\",\n                        style: {\n                            width: `${bufferedPercentage}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 h-full bg-primary-500 rounded-full transition-all duration-200\",\n                        style: {\n                            width: `${progressPercentage}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"absolute top-1/2 w-3 h-3 bg-primary-500 rounded-full transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                        style: {\n                            left: `${progressPercentage}%`,\n                            marginLeft: \"-6px\"\n                        },\n                        whileHover: {\n                            scale: 1.2\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    hoverTime !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 w-1 h-4 bg-white bg-opacity-60 transform -translate-y-1/2 pointer-events-none\",\n                        style: {\n                            left: `${hoverPosition}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\ProgressBar.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgressBar, \"pARwPNW/tOby/VQ9OSBLQ5pIaXI=\");\n_c = ProgressBar;\nvar _c;\n$RefreshReg$(_c, \"ProgressBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/player/ProgressBar.tsx\n"));

/***/ }),

/***/ "./src/components/player/QualitySelector.tsx":
/*!***************************************************!*\
  !*** ./src/components/player/QualitySelector.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualityIndicator: function() { return /* binding */ QualityIndicator; },\n/* harmony export */   QualitySelector: function() { return /* binding */ QualitySelector; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Settings,Zap!=!lucide-react */ \"__barrel_optimize__?names=Check,Settings,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n/**\n * 画质选择组件\n */ function QualitySelector({ qualities, currentQuality, onQualityChange, className }) {\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const menuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (menuRef.current && !menuRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    const currentQualityInfo = qualities.find((q)=>q.value === currentQuality);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative\", className),\n        ref: menuRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors\", \"bg-black/20 hover:bg-black/30 text-white text-sm\", isOpen && \"bg-black/40\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Settings, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: currentQualityInfo?.label || \"自动\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    transition: {\n                        duration: 0.15\n                    },\n                    className: \"absolute bottom-full right-0 mb-2 min-w-[160px] bg-black/90 backdrop-blur-sm rounded-lg shadow-xl border border-white/10 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 text-xs text-gray-400 font-medium border-b border-white/10\",\n                                children: \"画质选择\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this),\n                            qualities.map((quality, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.1,\n                                        delay: index * 0.05\n                                    },\n                                    onClick: ()=>{\n                                        onQualityChange(quality.value);\n                                        setIsOpen(false);\n                                    },\n                                    className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full flex items-center justify-between px-3 py-2 text-sm transition-colors\", \"hover:bg-white/10\", currentQuality === quality.value ? \"text-primary-400 bg-primary-500/20\" : \"text-white\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: quality.label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, this),\n                                                quality.value === \"auto\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Zap, {\n                                                    className: \"w-3 h-3 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentQuality === quality.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                            className: \"w-4 h-4 text-primary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, quality.value, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 text-xs text-gray-400 font-medium border-t border-white/10 mt-2\",\n                                children: \"播放速度\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this),\n                            [\n                                0.5,\n                                0.75,\n                                1,\n                                1.25,\n                                1.5,\n                                2\n                            ].map((speed, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.1,\n                                        delay: (qualities.length + index) * 0.05\n                                    },\n                                    onClick: ()=>{\n                                        // 这里可以添加播放速度变化的回调\n                                        setIsOpen(false);\n                                    },\n                                    className: \"w-full flex items-center justify-between px-3 py-2 text-sm text-white hover:bg-white/10 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                speed,\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this),\n                                        speed === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"正常\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 35\n                                        }, this)\n                                    ]\n                                }, speed, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(QualitySelector, \"yIsVPXmGJnWJAXf4YKobPzEQ+oo=\");\n_c = QualitySelector;\nfunction QualityIndicator({ quality, className }) {\n    const getQualityColor = (q)=>{\n        switch(q){\n            case \"1080p\":\n            case \"original\":\n                return \"text-green-400\";\n            case \"720p\":\n                return \"text-blue-400\";\n            case \"480p\":\n                return \"text-yellow-400\";\n            case \"auto\":\n                return \"text-purple-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-2 h-2 rounded-full\", getQualityColor(quality))\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs font-medium\", getQualityColor(quality)),\n                children: quality.toUpperCase()\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\QualitySelector.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_c1 = QualityIndicator;\nvar _c, _c1;\n$RefreshReg$(_c, \"QualitySelector\");\n$RefreshReg$(_c1, \"QualityIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/player/QualitySelector.tsx\n"));

/***/ }),

/***/ "./src/components/player/SettingsMenu.tsx":
/*!************************************************!*\
  !*** ./src/components/player/SettingsMenu.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsMenu: function() { return /* binding */ SettingsMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check!=!lucide-react */ \"__barrel_optimize__?names=Check!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"./src/lib/constants.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\n\n\n\n\n\n/**\n * 播放器设置菜单组件\n */ function SettingsMenu({ playbackRate, quality, onPlaybackRateChange, onQualityChange, onClose, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.9,\n            y: 10\n        },\n        animate: {\n            opacity: 1,\n            scale: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.9,\n            y: 10\n        },\n        transition: {\n            duration: 0.2\n        },\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute bottom-16 right-4 bg-black bg-opacity-90 backdrop-blur-sm rounded-lg p-4 min-w-[200px] z-10\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white text-sm font-medium mb-2\",\n                        children: \"播放速度\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.PLAYBACK_SPEEDS.map((speed)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onPlaybackRateChange(speed.value);\n                                    onClose();\n                                },\n                                className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full flex items-center justify-between px-3 py-2 text-sm rounded hover:bg-white hover:bg-opacity-10 transition-colors\", playbackRate === speed.value ? \"text-primary-400 bg-white bg-opacity-10\" : \"text-white\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: speed.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    playbackRate === speed.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, speed.value, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white text-sm font-medium mb-2\",\n                        children: \"视频质量\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VIDEO_QUALITIES.map((qualityOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onQualityChange(qualityOption.value);\n                                    onClose();\n                                },\n                                className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full flex items-center justify-between px-3 py-2 text-sm rounded hover:bg-white hover:bg-opacity-10 transition-colors\", quality === qualityOption.value ? \"text-primary-400 bg-white bg-opacity-10\" : \"text-white\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: qualityOption.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    quality === qualityOption.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, qualityOption.value, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-3 border-t border-white border-opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"w-full px-3 py-2 text-sm text-white hover:bg-white hover:bg-opacity-10 rounded transition-colors\",\n                    children: \"关闭\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\SettingsMenu.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c = SettingsMenu;\nvar _c;\n$RefreshReg$(_c, \"SettingsMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/player/SettingsMenu.tsx\n"));

/***/ }),

/***/ "./src/components/player/VideoPlayer.tsx":
/*!***********************************************!*\
  !*** ./src/components/player/VideoPlayer.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VideoPlayer: function() { return /* binding */ VideoPlayer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Loader_Maximize_Minimize_Play_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader,Maximize,Minimize,Play,Settings!=!lucide-react */ \"__barrel_optimize__?names=Loader,Maximize,Minimize,Play,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _PlayerControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PlayerControls */ \"./src/components/player/PlayerControls.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressBar */ \"./src/components/player/ProgressBar.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./VolumeControl */ \"./src/components/player/VolumeControl.tsx\");\n/* harmony import */ var _SettingsMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SettingsMenu */ \"./src/components/player/SettingsMenu.tsx\");\n/* harmony import */ var _hooks_usePlayer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/usePlayer */ \"./src/hooks/usePlayer.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\n * 视频播放器组件\n */ function VideoPlayer({ drama, videoUrl, poster, autoPlay = false, className, onEnded, onError }) {\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [showControls, setShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [controlsTimeout, setControlsTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const { playerState, currentDrama, play, pause, togglePlay, setVolume, toggleMute, seekTo, fastForward, rewind, setPlaybackRate, setQuality, enterFullscreen, exitFullscreen, toggleFullscreen, setLoading, setError, updateCurrentTime, updateDuration, loadDrama } = (0,_hooks_usePlayer__WEBPACK_IMPORTED_MODULE_6__.usePlayer)();\n    /**\n   * 隐藏控制栏的定时器\n   */ const hideControlsTimer = ()=>{\n        if (controlsTimeout) {\n            clearTimeout(controlsTimeout);\n        }\n        const timeout = setTimeout(()=>{\n            if (!isHovering && playerState.isPlaying) {\n                setShowControls(false);\n            }\n        }, 3000);\n        setControlsTimeout(timeout);\n    };\n    /**\n   * 显示控制栏\n   */ const showControlsHandler = ()=>{\n        setShowControls(true);\n        hideControlsTimer();\n    };\n    /**\n   * 处理鼠标移动\n   */ const handleMouseMove = ()=>{\n        showControlsHandler();\n    };\n    /**\n   * 处理鼠标进入\n   */ const handleMouseEnter = ()=>{\n        setIsHovering(true);\n        showControlsHandler();\n    };\n    /**\n   * 处理鼠标离开\n   */ const handleMouseLeave = ()=>{\n        setIsHovering(false);\n        hideControlsTimer();\n    };\n    /**\n   * 处理视频加载\n   */ const handleLoadStart = ()=>{\n        setLoading(true);\n    };\n    /**\n   * 处理视频可以播放\n   */ const handleCanPlay = ()=>{\n        setLoading(false);\n        if (videoRef.current) {\n            updateDuration(videoRef.current.duration);\n        }\n    };\n    /**\n   * 处理时间更新\n   */ const handleTimeUpdate = ()=>{\n        if (videoRef.current) {\n            updateCurrentTime(videoRef.current.currentTime);\n        }\n    };\n    /**\n   * 处理播放结束\n   */ const handleEnded = ()=>{\n        pause();\n        onEnded?.();\n    };\n    /**\n   * 处理错误\n   */ const handleError = ()=>{\n        const errorMessage = \"视频加载失败，请检查网络连接或稍后重试\";\n        setError(errorMessage);\n        onError?.(errorMessage);\n    };\n    /**\n   * 处理播放/暂停\n   */ const handlePlayPause = ()=>{\n        if (!videoRef.current) return;\n        if (playerState.isPlaying) {\n            videoRef.current.pause();\n            pause();\n        } else {\n            videoRef.current.play();\n            play();\n        }\n    };\n    /**\n   * 处理进度跳转\n   */ const handleSeek = (time)=>{\n        if (videoRef.current) {\n            videoRef.current.currentTime = time;\n            seekTo(time);\n        }\n    };\n    /**\n   * 处理音量变化\n   */ const handleVolumeChange = (volume)=>{\n        if (videoRef.current) {\n            videoRef.current.volume = volume;\n            setVolume(volume);\n        }\n    };\n    /**\n   * 处理静音切换\n   */ const handleMuteToggle = ()=>{\n        if (videoRef.current) {\n            const newVolume = playerState.volume > 0 ? 0 : 0.8;\n            videoRef.current.volume = newVolume;\n            toggleMute();\n        }\n    };\n    /**\n   * 处理播放速度变化\n   */ const handlePlaybackRateChange = (rate)=>{\n        if (videoRef.current) {\n            videoRef.current.playbackRate = rate;\n            setPlaybackRate(rate);\n        }\n    };\n    /**\n   * 处理全屏切换\n   */ const handleFullscreenToggle = ()=>{\n        if (!containerRef.current) return;\n        if (playerState.isFullscreen) {\n            document.exitFullscreen();\n            exitFullscreen();\n        } else {\n            containerRef.current.requestFullscreen();\n            enterFullscreen();\n        }\n    };\n    /**\n   * 处理键盘快捷键\n   */ const handleKeyDown = (event)=>{\n        if (!videoRef.current) return;\n        switch(event.code){\n            case \"Space\":\n                event.preventDefault();\n                handlePlayPause();\n                break;\n            case \"ArrowLeft\":\n                event.preventDefault();\n                rewind(10);\n                handleSeek(Math.max(0, playerState.currentTime - 10));\n                break;\n            case \"ArrowRight\":\n                event.preventDefault();\n                fastForward(10);\n                handleSeek(Math.min(playerState.duration, playerState.currentTime + 10));\n                break;\n            case \"ArrowUp\":\n                event.preventDefault();\n                handleVolumeChange(Math.min(1, playerState.volume + 0.1));\n                break;\n            case \"ArrowDown\":\n                event.preventDefault();\n                handleVolumeChange(Math.max(0, playerState.volume - 0.1));\n                break;\n            case \"KeyF\":\n                event.preventDefault();\n                handleFullscreenToggle();\n                break;\n            case \"KeyM\":\n                event.preventDefault();\n                handleMuteToggle();\n                break;\n        }\n    };\n    /**\n   * 初始化播放器\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDrama(drama);\n        // 添加键盘事件监听\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown);\n            if (controlsTimeout) {\n                clearTimeout(controlsTimeout);\n            }\n        };\n    }, [\n        drama\n    ]);\n    /**\n   * 监听全屏状态变化\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleFullscreenChange = ()=>{\n            const isFullscreen = !!document.fullscreenElement;\n            if (isFullscreen !== playerState.isFullscreen) {\n                if (isFullscreen) {\n                    enterFullscreen();\n                } else {\n                    exitFullscreen();\n                }\n            }\n        };\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n        return ()=>{\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n        };\n    }, [\n        playerState.isFullscreen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"relative bg-black rounded-lg overflow-hidden group\", playerState.isFullscreen && \"fixed inset-0 z-50 rounded-none\", className),\n        onMouseMove: handleMouseMove,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                ref: videoRef,\n                className: \"w-full h-full object-contain\",\n                poster: poster || drama.cover,\n                autoPlay: autoPlay,\n                playsInline: true,\n                onLoadStart: handleLoadStart,\n                onCanPlay: handleCanPlay,\n                onTimeUpdate: handleTimeUpdate,\n                onEnded: handleEnded,\n                onError: handleError,\n                onClick: handlePlayPause,\n                children: [\n                    videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: videoUrl,\n                        type: \"video/mp4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 22\n                    }, this),\n                    \"您的浏览器不支持视频播放。\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            playerState.isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_Maximize_Minimize_Play_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Loader, {\n                    className: \"w-12 h-12 text-white animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, this),\n            !playerState.isPlaying && !playerState.isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30\",\n                onClick: handlePlayPause,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    whileTap: {\n                        scale: 0.9\n                    },\n                    className: \"w-20 h-20 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_Maximize_Minimize_Play_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Play, {\n                        className: \"w-8 h-8 text-gray-900 ml-1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_3__.ProgressBar, {\n                            currentTime: playerState.currentTime,\n                            duration: playerState.duration,\n                            onSeek: handleSeek,\n                            className: \"mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayerControls__WEBPACK_IMPORTED_MODULE_2__.PlayerControls, {\n                                            isPlaying: playerState.isPlaying,\n                                            onPlayPause: handlePlayPause,\n                                            onRewind: ()=>handleSeek(Math.max(0, playerState.currentTime - 10)),\n                                            onFastForward: ()=>handleSeek(Math.min(playerState.duration, playerState.currentTime + 10))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_4__.VolumeControl, {\n                                            volume: playerState.volume,\n                                            onVolumeChange: handleVolumeChange,\n                                            onMuteToggle: handleMuteToggle\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm\",\n                                            children: [\n                                                Math.floor(playerState.currentTime / 60),\n                                                \":\",\n                                                Math.floor(playerState.currentTime % 60).toString().padStart(2, \"0\"),\n                                                \" / \",\n                                                Math.floor(playerState.duration / 60),\n                                                \":\",\n                                                Math.floor(playerState.duration % 60).toString().padStart(2, \"0\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSettings(!showSettings),\n                                            className: \"p-2 text-white hover:bg-white hover:bg-opacity-20 rounded transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_Maximize_Minimize_Play_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Settings, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleFullscreenToggle,\n                                            className: \"p-2 text-white hover:bg-white hover:bg-opacity-20 rounded transition-colors\",\n                                            children: playerState.isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_Maximize_Minimize_Play_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Minimize, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader_Maximize_Minimize_Play_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Maximize, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SettingsMenu__WEBPACK_IMPORTED_MODULE_5__.SettingsMenu, {\n                playbackRate: playerState.playbackRate,\n                quality: playerState.quality,\n                onPlaybackRateChange: handlePlaybackRateChange,\n                onQualityChange: setQuality,\n                onClose: ()=>setShowSettings(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this),\n            playerState.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg font-medium mb-2\",\n                            children: \"播放出错\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-300 mb-4\",\n                            children: playerState.error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setError(undefined),\n                            className: \"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VideoPlayer.tsx\",\n        lineNumber: 316,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoPlayer, \"9uIRBhuBTa94PCW6OpJ4F9trqwQ=\", false, function() {\n    return [\n        _hooks_usePlayer__WEBPACK_IMPORTED_MODULE_6__.usePlayer\n    ];\n});\n_c = VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wbGF5ZXIvVmlkZW9QbGF5ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEyRDtBQUNIO0FBWWxDO0FBQzRCO0FBQ047QUFDSTtBQUNGO0FBQ0E7QUFDakI7QUFhN0I7O0NBRUMsR0FDTSxTQUFTaUIsWUFBWSxFQUMxQkMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLE1BQU0sRUFDTkMsV0FBVyxLQUFLLEVBQ2hCQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsT0FBTyxFQUNVOztJQUNqQixNQUFNQyxXQUFXeEIsNkNBQU1BLENBQW1CO0lBQzFDLE1BQU15QixlQUFlekIsNkNBQU1BLENBQWlCO0lBQzVDLE1BQU0sQ0FBQzBCLGNBQWNDLGdCQUFnQixHQUFHekIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDMEIsY0FBY0MsZ0JBQWdCLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM0QixZQUFZQyxjQUFjLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM4QixpQkFBaUJDLG1CQUFtQixHQUFHL0IsK0NBQVFBO0lBRXRELE1BQU0sRUFDSmdDLFdBQVcsRUFDWEMsWUFBWSxFQUNaQyxJQUFJLEVBQ0pDLEtBQUssRUFDTEMsVUFBVSxFQUNWQyxTQUFTLEVBQ1RDLFVBQVUsRUFDVkMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLE1BQU0sRUFDTkMsZUFBZSxFQUNmQyxVQUFVLEVBQ1ZDLGVBQWUsRUFDZkMsY0FBYyxFQUNkQyxnQkFBZ0IsRUFDaEJDLFVBQVUsRUFDVkMsUUFBUSxFQUNSQyxpQkFBaUIsRUFDakJDLGNBQWMsRUFDZEMsU0FBUyxFQUNWLEdBQUd2QywyREFBU0E7SUFFYjs7R0FFQyxHQUNELE1BQU13QyxvQkFBb0I7UUFDeEIsSUFBSXRCLGlCQUFpQjtZQUNuQnVCLGFBQWF2QjtRQUNmO1FBRUEsTUFBTXdCLFVBQVVDLFdBQVc7WUFDekIsSUFBSSxDQUFDM0IsY0FBY0ksWUFBWXdCLFNBQVMsRUFBRTtnQkFDeEMvQixnQkFBZ0I7WUFDbEI7UUFDRixHQUFHO1FBRUhNLG1CQUFtQnVCO0lBQ3JCO0lBRUE7O0dBRUMsR0FDRCxNQUFNRyxzQkFBc0I7UUFDMUJoQyxnQkFBZ0I7UUFDaEIyQjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNTSxrQkFBa0I7UUFDdEJEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1FLG1CQUFtQjtRQUN2QjlCLGNBQWM7UUFDZDRCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1HLG1CQUFtQjtRQUN2Qi9CLGNBQWM7UUFDZHVCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1TLGtCQUFrQjtRQUN0QmQsV0FBVztJQUNiO0lBRUE7O0dBRUMsR0FDRCxNQUFNZSxnQkFBZ0I7UUFDcEJmLFdBQVc7UUFDWCxJQUFJekIsU0FBU3lDLE9BQU8sRUFBRTtZQUNwQmIsZUFBZTVCLFNBQVN5QyxPQUFPLENBQUNDLFFBQVE7UUFDMUM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsbUJBQW1CO1FBQ3ZCLElBQUkzQyxTQUFTeUMsT0FBTyxFQUFFO1lBQ3BCZCxrQkFBa0IzQixTQUFTeUMsT0FBTyxDQUFDRyxXQUFXO1FBQ2hEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1DLGNBQWM7UUFDbEJoQztRQUNBZjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNZ0QsY0FBYztRQUNsQixNQUFNQyxlQUFlO1FBQ3JCckIsU0FBU3FCO1FBQ1RoRCxVQUFVZ0Q7SUFDWjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsa0JBQWtCO1FBQ3RCLElBQUksQ0FBQ2hELFNBQVN5QyxPQUFPLEVBQUU7UUFFdkIsSUFBSS9CLFlBQVl3QixTQUFTLEVBQUU7WUFDekJsQyxTQUFTeUMsT0FBTyxDQUFDNUIsS0FBSztZQUN0QkE7UUFDRixPQUFPO1lBQ0xiLFNBQVN5QyxPQUFPLENBQUM3QixJQUFJO1lBQ3JCQTtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1xQyxhQUFhLENBQUNDO1FBQ2xCLElBQUlsRCxTQUFTeUMsT0FBTyxFQUFFO1lBQ3BCekMsU0FBU3lDLE9BQU8sQ0FBQ0csV0FBVyxHQUFHTTtZQUMvQmpDLE9BQU9pQztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixJQUFJcEQsU0FBU3lDLE9BQU8sRUFBRTtZQUNwQnpDLFNBQVN5QyxPQUFPLENBQUNXLE1BQU0sR0FBR0E7WUFDMUJyQyxVQUFVcUM7UUFDWjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxtQkFBbUI7UUFDdkIsSUFBSXJELFNBQVN5QyxPQUFPLEVBQUU7WUFDcEIsTUFBTWEsWUFBWTVDLFlBQVkwQyxNQUFNLEdBQUcsSUFBSSxJQUFJO1lBQy9DcEQsU0FBU3lDLE9BQU8sQ0FBQ1csTUFBTSxHQUFHRTtZQUMxQnRDO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTXVDLDJCQUEyQixDQUFDQztRQUNoQyxJQUFJeEQsU0FBU3lDLE9BQU8sRUFBRTtZQUNwQnpDLFNBQVN5QyxPQUFPLENBQUNnQixZQUFZLEdBQUdEO1lBQ2hDcEMsZ0JBQWdCb0M7UUFDbEI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUUseUJBQXlCO1FBQzdCLElBQUksQ0FBQ3pELGFBQWF3QyxPQUFPLEVBQUU7UUFFM0IsSUFBSS9CLFlBQVlpRCxZQUFZLEVBQUU7WUFDNUJDLFNBQVNyQyxjQUFjO1lBQ3ZCQTtRQUNGLE9BQU87WUFDTHRCLGFBQWF3QyxPQUFPLENBQUNvQixpQkFBaUI7WUFDdEN2QztRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU13QyxnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSSxDQUFDL0QsU0FBU3lDLE9BQU8sRUFBRTtRQUV2QixPQUFRc0IsTUFBTUMsSUFBSTtZQUNoQixLQUFLO2dCQUNIRCxNQUFNRSxjQUFjO2dCQUNwQmpCO2dCQUNBO1lBQ0YsS0FBSztnQkFDSGUsTUFBTUUsY0FBYztnQkFDcEI5QyxPQUFPO2dCQUNQOEIsV0FBV2lCLEtBQUtDLEdBQUcsQ0FBQyxHQUFHekQsWUFBWWtDLFdBQVcsR0FBRztnQkFDakQ7WUFDRixLQUFLO2dCQUNIbUIsTUFBTUUsY0FBYztnQkFDcEIvQyxZQUFZO2dCQUNaK0IsV0FBV2lCLEtBQUtFLEdBQUcsQ0FBQzFELFlBQVlnQyxRQUFRLEVBQUVoQyxZQUFZa0MsV0FBVyxHQUFHO2dCQUNwRTtZQUNGLEtBQUs7Z0JBQ0htQixNQUFNRSxjQUFjO2dCQUNwQmQsbUJBQW1CZSxLQUFLRSxHQUFHLENBQUMsR0FBRzFELFlBQVkwQyxNQUFNLEdBQUc7Z0JBQ3BEO1lBQ0YsS0FBSztnQkFDSFcsTUFBTUUsY0FBYztnQkFDcEJkLG1CQUFtQmUsS0FBS0MsR0FBRyxDQUFDLEdBQUd6RCxZQUFZMEMsTUFBTSxHQUFHO2dCQUNwRDtZQUNGLEtBQUs7Z0JBQ0hXLE1BQU1FLGNBQWM7Z0JBQ3BCUDtnQkFDQTtZQUNGLEtBQUs7Z0JBQ0hLLE1BQU1FLGNBQWM7Z0JBQ3BCWjtnQkFDQTtRQUNKO0lBQ0Y7SUFFQTs7R0FFQyxHQUNENUUsZ0RBQVNBLENBQUM7UUFDUm9ELFVBQVVwQztRQUVWLFdBQVc7UUFDWG1FLFNBQVNTLGdCQUFnQixDQUFDLFdBQVdQO1FBRXJDLE9BQU87WUFDTEYsU0FBU1UsbUJBQW1CLENBQUMsV0FBV1I7WUFDeEMsSUFBSXRELGlCQUFpQjtnQkFDbkJ1QixhQUFhdkI7WUFDZjtRQUNGO0lBQ0YsR0FBRztRQUFDZjtLQUFNO0lBRVY7O0dBRUMsR0FDRGhCLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTThGLHlCQUF5QjtZQUM3QixNQUFNWixlQUFlLENBQUMsQ0FBQ0MsU0FBU1ksaUJBQWlCO1lBQ2pELElBQUliLGlCQUFpQmpELFlBQVlpRCxZQUFZLEVBQUU7Z0JBQzdDLElBQUlBLGNBQWM7b0JBQ2hCckM7Z0JBQ0YsT0FBTztvQkFDTEM7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUFxQyxTQUFTUyxnQkFBZ0IsQ0FBQyxvQkFBb0JFO1FBQzlDLE9BQU87WUFDTFgsU0FBU1UsbUJBQW1CLENBQUMsb0JBQW9CQztRQUNuRDtJQUNGLEdBQUc7UUFBQzdELFlBQVlpRCxZQUFZO0tBQUM7SUFFN0IscUJBQ0UsOERBQUNjO1FBQ0NDLEtBQUt6RTtRQUNMSixXQUFXTiwwQ0FBRUEsQ0FDWCxzREFDQW1CLFlBQVlpRCxZQUFZLElBQUksbUNBQzVCOUQ7UUFFRjhFLGFBQWF2QztRQUNid0MsY0FBY3ZDO1FBQ2R3QyxjQUFjdkM7OzBCQUdkLDhEQUFDd0M7Z0JBQ0NKLEtBQUsxRTtnQkFDTEgsV0FBVTtnQkFDVkYsUUFBUUEsVUFBVUYsTUFBTXNGLEtBQUs7Z0JBQzdCbkYsVUFBVUE7Z0JBQ1ZvRixXQUFXO2dCQUNYQyxhQUFhMUM7Z0JBQ2IyQyxXQUFXMUM7Z0JBQ1gyQyxjQUFjeEM7Z0JBQ2Q3QyxTQUFTK0M7Z0JBQ1Q5QyxTQUFTK0M7Z0JBQ1RzQyxTQUFTcEM7O29CQUVSdEQsMEJBQVksOERBQUMyRjt3QkFBT0MsS0FBSzVGO3dCQUFVNkYsTUFBSzs7Ozs7O29CQUFlOzs7Ozs7O1lBS3pEN0UsWUFBWThFLFNBQVMsa0JBQ3BCLDhEQUFDZjtnQkFBSTVFLFdBQVU7MEJBQ2IsNEVBQUNaLDhHQUFNQTtvQkFBQ1ksV0FBVTs7Ozs7Ozs7Ozs7WUFLckIsQ0FBQ2EsWUFBWXdCLFNBQVMsSUFBSSxDQUFDeEIsWUFBWThFLFNBQVMsa0JBQy9DLDhEQUFDN0csaURBQU1BLENBQUM4RixHQUFHO2dCQUNUZ0IsU0FBUztvQkFBRUMsU0FBUztvQkFBR0MsT0FBTztnQkFBSTtnQkFDbENDLFNBQVM7b0JBQUVGLFNBQVM7b0JBQUdDLE9BQU87Z0JBQUU7Z0JBQ2hDRSxNQUFNO29CQUFFSCxTQUFTO29CQUFHQyxPQUFPO2dCQUFJO2dCQUMvQjlGLFdBQVU7Z0JBQ1Z1RixTQUFTcEM7MEJBRVQsNEVBQUNyRSxpREFBTUEsQ0FBQ21ILE1BQU07b0JBQ1pDLFlBQVk7d0JBQUVKLE9BQU87b0JBQUk7b0JBQ3pCSyxVQUFVO3dCQUFFTCxPQUFPO29CQUFJO29CQUN2QjlGLFdBQVU7OEJBRVYsNEVBQUNoQiw0R0FBSUE7d0JBQUNnQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBCQU10Qiw4REFBQ2pCLDBEQUFlQTswQkFDYnNCLDhCQUNDLDhEQUFDdkIsaURBQU1BLENBQUM4RixHQUFHO29CQUNUZ0IsU0FBUzt3QkFBRUMsU0FBUzt3QkFBR08sR0FBRztvQkFBRztvQkFDN0JMLFNBQVM7d0JBQUVGLFNBQVM7d0JBQUdPLEdBQUc7b0JBQUU7b0JBQzVCSixNQUFNO3dCQUFFSCxTQUFTO3dCQUFHTyxHQUFHO29CQUFHO29CQUMxQkMsWUFBWTt3QkFBRXhELFVBQVU7b0JBQUk7b0JBQzVCN0MsV0FBVTs7c0NBR1YsOERBQUNWLHFEQUFXQTs0QkFDVnlELGFBQWFsQyxZQUFZa0MsV0FBVzs0QkFDcENGLFVBQVVoQyxZQUFZZ0MsUUFBUTs0QkFDOUJ5RCxRQUFRbEQ7NEJBQ1JwRCxXQUFVOzs7Ozs7c0NBSVosOERBQUM0RTs0QkFBSTVFLFdBQVU7OzhDQUNiLDhEQUFDNEU7b0NBQUk1RSxXQUFVOztzREFDYiw4REFBQ1gsMkRBQWNBOzRDQUNiZ0QsV0FBV3hCLFlBQVl3QixTQUFTOzRDQUNoQ2tFLGFBQWFwRDs0Q0FDYnFELFVBQVUsSUFBTXBELFdBQVdpQixLQUFLQyxHQUFHLENBQUMsR0FBR3pELFlBQVlrQyxXQUFXLEdBQUc7NENBQ2pFMEQsZUFBZSxJQUFNckQsV0FBV2lCLEtBQUtFLEdBQUcsQ0FBQzFELFlBQVlnQyxRQUFRLEVBQUVoQyxZQUFZa0MsV0FBVyxHQUFHOzs7Ozs7c0RBRzNGLDhEQUFDeEQseURBQWFBOzRDQUNaZ0UsUUFBUTFDLFlBQVkwQyxNQUFNOzRDQUMxQm1ELGdCQUFnQnBEOzRDQUNoQnFELGNBQWNuRDs7Ozs7O3NEQUdoQiw4REFBQ29COzRDQUFJNUUsV0FBVTs7Z0RBQ1pxRSxLQUFLdUMsS0FBSyxDQUFDL0YsWUFBWWtDLFdBQVcsR0FBRztnREFBSTtnREFBRXNCLEtBQUt1QyxLQUFLLENBQUMvRixZQUFZa0MsV0FBVyxHQUFHLElBQUk4RCxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO2dEQUFLO2dEQUFJekMsS0FBS3VDLEtBQUssQ0FBQy9GLFlBQVlnQyxRQUFRLEdBQUc7Z0RBQUk7Z0RBQUV3QixLQUFLdUMsS0FBSyxDQUFDL0YsWUFBWWdDLFFBQVEsR0FBRyxJQUFJZ0UsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRzs7Ozs7Ozs7Ozs7Ozs4Q0FJOU4sOERBQUNsQztvQ0FBSTVFLFdBQVU7O3NEQUNiLDhEQUFDaUc7NENBQ0NWLFNBQVMsSUFBTS9FLGdCQUFnQixDQUFDRDs0Q0FDaENQLFdBQVU7c0RBRVYsNEVBQUNiLGdIQUFRQTtnREFBQ2EsV0FBVTs7Ozs7Ozs7Ozs7c0RBR3RCLDhEQUFDaUc7NENBQ0NWLFNBQVMxQjs0Q0FDVDdELFdBQVU7c0RBRVRhLFlBQVlpRCxZQUFZLGlCQUN2Qiw4REFBQzVFLGdIQUFRQTtnREFBQ2MsV0FBVTs7Ozs7cUVBRXBCLDhEQUFDZixnSEFBUUE7Z0RBQUNlLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVakNPLDhCQUNDLDhEQUFDZix1REFBWUE7Z0JBQ1hvRSxjQUFjL0MsWUFBWStDLFlBQVk7Z0JBQ3RDbUQsU0FBU2xHLFlBQVlrRyxPQUFPO2dCQUM1QkMsc0JBQXNCdEQ7Z0JBQ3RCdUQsaUJBQWlCekY7Z0JBQ2pCMEYsU0FBUyxJQUFNMUcsZ0JBQWdCOzs7Ozs7WUFLbENLLFlBQVlzRyxLQUFLLGtCQUNoQiw4REFBQ3ZDO2dCQUFJNUUsV0FBVTswQkFDYiw0RUFBQzRFO29CQUFJNUUsV0FBVTs7c0NBQ2IsOERBQUM0RTs0QkFBSTVFLFdBQVU7c0NBQTJCOzs7Ozs7c0NBQzFDLDhEQUFDNEU7NEJBQUk1RSxXQUFVO3NDQUE4QmEsWUFBWXNHLEtBQUs7Ozs7OztzQ0FDOUQsOERBQUNsQjs0QkFDQ1YsU0FBUyxJQUFNMUQsU0FBU3VGOzRCQUN4QnBILFdBQVU7c0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0ExYWdCTDs7UUFxQ1ZGLHVEQUFTQTs7O0tBckNDRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9wbGF5ZXIvVmlkZW9QbGF5ZXIudHN4P2U2MGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVJlZiwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBcbiAgUGxheSwgXG4gIFBhdXNlLCBcbiAgVm9sdW1lMiwgXG4gIFZvbHVtZVgsIFxuICBNYXhpbWl6ZSwgXG4gIE1pbmltaXplLFxuICBTa2lwQmFjayxcbiAgU2tpcEZvcndhcmQsXG4gIFNldHRpbmdzLFxuICBMb2FkZXJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IFBsYXllckNvbnRyb2xzIH0gZnJvbSAnLi9QbGF5ZXJDb250cm9scyc7XG5pbXBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInO1xuaW1wb3J0IHsgVm9sdW1lQ29udHJvbCB9IGZyb20gJy4vVm9sdW1lQ29udHJvbCc7XG5pbXBvcnQgeyBTZXR0aW5nc01lbnUgfSBmcm9tICcuL1NldHRpbmdzTWVudSc7XG5pbXBvcnQgeyB1c2VQbGF5ZXIgfSBmcm9tICdAL2hvb2tzL3VzZVBsYXllcic7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvdXRpbHMnO1xuaW1wb3J0IHR5cGUgeyBEcmFtYSB9IGZyb20gJ0AvdHlwZXMnO1xuXG5pbnRlcmZhY2UgVmlkZW9QbGF5ZXJQcm9wcyB7XG4gIGRyYW1hOiBEcmFtYTtcbiAgdmlkZW9Vcmw/OiBzdHJpbmc7XG4gIHBvc3Rlcj86IHN0cmluZztcbiAgYXV0b1BsYXk/OiBib29sZWFuO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIG9uRW5kZWQ/OiAoKSA9PiB2b2lkO1xuICBvbkVycm9yPzogKGVycm9yOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbi8qKlxuICog6KeG6aKR5pKt5pS+5Zmo57uE5Lu2XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBWaWRlb1BsYXllcih7XG4gIGRyYW1hLFxuICB2aWRlb1VybCxcbiAgcG9zdGVyLFxuICBhdXRvUGxheSA9IGZhbHNlLFxuICBjbGFzc05hbWUsXG4gIG9uRW5kZWQsXG4gIG9uRXJyb3IsXG59OiBWaWRlb1BsYXllclByb3BzKSB7XG4gIGNvbnN0IHZpZGVvUmVmID0gdXNlUmVmPEhUTUxWaWRlb0VsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBjb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBbc2hvd0NvbnRyb2xzLCBzZXRTaG93Q29udHJvbHNdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzaG93U2V0dGluZ3MsIHNldFNob3dTZXR0aW5nc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0hvdmVyaW5nLCBzZXRJc0hvdmVyaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NvbnRyb2xzVGltZW91dCwgc2V0Q29udHJvbHNUaW1lb3V0XSA9IHVzZVN0YXRlPE5vZGVKUy5UaW1lb3V0PigpO1xuXG4gIGNvbnN0IHtcbiAgICBwbGF5ZXJTdGF0ZSxcbiAgICBjdXJyZW50RHJhbWEsXG4gICAgcGxheSxcbiAgICBwYXVzZSxcbiAgICB0b2dnbGVQbGF5LFxuICAgIHNldFZvbHVtZSxcbiAgICB0b2dnbGVNdXRlLFxuICAgIHNlZWtUbyxcbiAgICBmYXN0Rm9yd2FyZCxcbiAgICByZXdpbmQsXG4gICAgc2V0UGxheWJhY2tSYXRlLFxuICAgIHNldFF1YWxpdHksXG4gICAgZW50ZXJGdWxsc2NyZWVuLFxuICAgIGV4aXRGdWxsc2NyZWVuLFxuICAgIHRvZ2dsZUZ1bGxzY3JlZW4sXG4gICAgc2V0TG9hZGluZyxcbiAgICBzZXRFcnJvcixcbiAgICB1cGRhdGVDdXJyZW50VGltZSxcbiAgICB1cGRhdGVEdXJhdGlvbixcbiAgICBsb2FkRHJhbWEsXG4gIH0gPSB1c2VQbGF5ZXIoKTtcblxuICAvKipcbiAgICog6ZqQ6JeP5o6n5Yi25qCP55qE5a6a5pe25ZmoXG4gICAqL1xuICBjb25zdCBoaWRlQ29udHJvbHNUaW1lciA9ICgpID0+IHtcbiAgICBpZiAoY29udHJvbHNUaW1lb3V0KSB7XG4gICAgICBjbGVhclRpbWVvdXQoY29udHJvbHNUaW1lb3V0KTtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgdGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaWYgKCFpc0hvdmVyaW5nICYmIHBsYXllclN0YXRlLmlzUGxheWluZykge1xuICAgICAgICBzZXRTaG93Q29udHJvbHMoZmFsc2UpO1xuICAgICAgfVxuICAgIH0sIDMwMDApO1xuICAgIFxuICAgIHNldENvbnRyb2xzVGltZW91dCh0aW1lb3V0KTtcbiAgfTtcblxuICAvKipcbiAgICog5pi+56S65o6n5Yi25qCPXG4gICAqL1xuICBjb25zdCBzaG93Q29udHJvbHNIYW5kbGVyID0gKCkgPT4ge1xuICAgIHNldFNob3dDb250cm9scyh0cnVlKTtcbiAgICBoaWRlQ29udHJvbHNUaW1lcigpO1xuICB9O1xuXG4gIC8qKlxuICAgKiDlpITnkIbpvKDmoIfnp7vliqhcbiAgICovXG4gIGNvbnN0IGhhbmRsZU1vdXNlTW92ZSA9ICgpID0+IHtcbiAgICBzaG93Q29udHJvbHNIYW5kbGVyKCk7XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhum8oOagh+i/m+WFpVxuICAgKi9cbiAgY29uc3QgaGFuZGxlTW91c2VFbnRlciA9ICgpID0+IHtcbiAgICBzZXRJc0hvdmVyaW5nKHRydWUpO1xuICAgIHNob3dDb250cm9sc0hhbmRsZXIoKTtcbiAgfTtcblxuICAvKipcbiAgICog5aSE55CG6byg5qCH56a75byAXG4gICAqL1xuICBjb25zdCBoYW5kbGVNb3VzZUxlYXZlID0gKCkgPT4ge1xuICAgIHNldElzSG92ZXJpbmcoZmFsc2UpO1xuICAgIGhpZGVDb250cm9sc1RpbWVyKCk7XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhuinhumikeWKoOi9vVxuICAgKi9cbiAgY29uc3QgaGFuZGxlTG9hZFN0YXJ0ID0gKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhuinhumikeWPr+S7peaSreaUvlxuICAgKi9cbiAgY29uc3QgaGFuZGxlQ2FuUGxheSA9ICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICBpZiAodmlkZW9SZWYuY3VycmVudCkge1xuICAgICAgdXBkYXRlRHVyYXRpb24odmlkZW9SZWYuY3VycmVudC5kdXJhdGlvbik7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiDlpITnkIbml7bpl7Tmm7TmlrBcbiAgICovXG4gIGNvbnN0IGhhbmRsZVRpbWVVcGRhdGUgPSAoKSA9PiB7XG4gICAgaWYgKHZpZGVvUmVmLmN1cnJlbnQpIHtcbiAgICAgIHVwZGF0ZUN1cnJlbnRUaW1lKHZpZGVvUmVmLmN1cnJlbnQuY3VycmVudFRpbWUpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICog5aSE55CG5pKt5pS+57uT5p2fXG4gICAqL1xuICBjb25zdCBoYW5kbGVFbmRlZCA9ICgpID0+IHtcbiAgICBwYXVzZSgpO1xuICAgIG9uRW5kZWQ/LigpO1xuICB9O1xuXG4gIC8qKlxuICAgKiDlpITnkIbplJnor69cbiAgICovXG4gIGNvbnN0IGhhbmRsZUVycm9yID0gKCkgPT4ge1xuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9ICfop4bpopHliqDovb3lpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqXmiJbnqI3lkI7ph43or5UnO1xuICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgb25FcnJvcj8uKGVycm9yTWVzc2FnZSk7XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhuaSreaUvi/mmoLlgZxcbiAgICovXG4gIGNvbnN0IGhhbmRsZVBsYXlQYXVzZSA9ICgpID0+IHtcbiAgICBpZiAoIXZpZGVvUmVmLmN1cnJlbnQpIHJldHVybjtcbiAgICBcbiAgICBpZiAocGxheWVyU3RhdGUuaXNQbGF5aW5nKSB7XG4gICAgICB2aWRlb1JlZi5jdXJyZW50LnBhdXNlKCk7XG4gICAgICBwYXVzZSgpO1xuICAgIH0gZWxzZSB7XG4gICAgICB2aWRlb1JlZi5jdXJyZW50LnBsYXkoKTtcbiAgICAgIHBsYXkoKTtcbiAgICB9XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhui/m+W6pui3s+i9rFxuICAgKi9cbiAgY29uc3QgaGFuZGxlU2VlayA9ICh0aW1lOiBudW1iZXIpID0+IHtcbiAgICBpZiAodmlkZW9SZWYuY3VycmVudCkge1xuICAgICAgdmlkZW9SZWYuY3VycmVudC5jdXJyZW50VGltZSA9IHRpbWU7XG4gICAgICBzZWVrVG8odGltZSk7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiDlpITnkIbpn7Pph4/lj5jljJZcbiAgICovXG4gIGNvbnN0IGhhbmRsZVZvbHVtZUNoYW5nZSA9ICh2b2x1bWU6IG51bWJlcikgPT4ge1xuICAgIGlmICh2aWRlb1JlZi5jdXJyZW50KSB7XG4gICAgICB2aWRlb1JlZi5jdXJyZW50LnZvbHVtZSA9IHZvbHVtZTtcbiAgICAgIHNldFZvbHVtZSh2b2x1bWUpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICog5aSE55CG6Z2Z6Z+z5YiH5o2iXG4gICAqL1xuICBjb25zdCBoYW5kbGVNdXRlVG9nZ2xlID0gKCkgPT4ge1xuICAgIGlmICh2aWRlb1JlZi5jdXJyZW50KSB7XG4gICAgICBjb25zdCBuZXdWb2x1bWUgPSBwbGF5ZXJTdGF0ZS52b2x1bWUgPiAwID8gMCA6IDAuODtcbiAgICAgIHZpZGVvUmVmLmN1cnJlbnQudm9sdW1lID0gbmV3Vm9sdW1lO1xuICAgICAgdG9nZ2xlTXV0ZSgpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICog5aSE55CG5pKt5pS+6YCf5bqm5Y+Y5YyWXG4gICAqL1xuICBjb25zdCBoYW5kbGVQbGF5YmFja1JhdGVDaGFuZ2UgPSAocmF0ZTogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHZpZGVvUmVmLmN1cnJlbnQpIHtcbiAgICAgIHZpZGVvUmVmLmN1cnJlbnQucGxheWJhY2tSYXRlID0gcmF0ZTtcbiAgICAgIHNldFBsYXliYWNrUmF0ZShyYXRlKTtcbiAgICB9XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhuWFqOWxj+WIh+aNolxuICAgKi9cbiAgY29uc3QgaGFuZGxlRnVsbHNjcmVlblRvZ2dsZSA9ICgpID0+IHtcbiAgICBpZiAoIWNvbnRhaW5lclJlZi5jdXJyZW50KSByZXR1cm47XG5cbiAgICBpZiAocGxheWVyU3RhdGUuaXNGdWxsc2NyZWVuKSB7XG4gICAgICBkb2N1bWVudC5leGl0RnVsbHNjcmVlbigpO1xuICAgICAgZXhpdEZ1bGxzY3JlZW4oKTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29udGFpbmVyUmVmLmN1cnJlbnQucmVxdWVzdEZ1bGxzY3JlZW4oKTtcbiAgICAgIGVudGVyRnVsbHNjcmVlbigpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICog5aSE55CG6ZSu55uY5b+r5o236ZSuXG4gICAqL1xuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgaWYgKCF2aWRlb1JlZi5jdXJyZW50KSByZXR1cm47XG5cbiAgICBzd2l0Y2ggKGV2ZW50LmNvZGUpIHtcbiAgICAgIGNhc2UgJ1NwYWNlJzpcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgaGFuZGxlUGxheVBhdXNlKCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnQXJyb3dMZWZ0JzpcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgcmV3aW5kKDEwKTtcbiAgICAgICAgaGFuZGxlU2VlayhNYXRoLm1heCgwLCBwbGF5ZXJTdGF0ZS5jdXJyZW50VGltZSAtIDEwKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnQXJyb3dSaWdodCc6XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIGZhc3RGb3J3YXJkKDEwKTtcbiAgICAgICAgaGFuZGxlU2VlayhNYXRoLm1pbihwbGF5ZXJTdGF0ZS5kdXJhdGlvbiwgcGxheWVyU3RhdGUuY3VycmVudFRpbWUgKyAxMCkpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ0Fycm93VXAnOlxuICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBoYW5kbGVWb2x1bWVDaGFuZ2UoTWF0aC5taW4oMSwgcGxheWVyU3RhdGUudm9sdW1lICsgMC4xKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnQXJyb3dEb3duJzpcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgaGFuZGxlVm9sdW1lQ2hhbmdlKE1hdGgubWF4KDAsIHBsYXllclN0YXRlLnZvbHVtZSAtIDAuMSkpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ0tleUYnOlxuICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBoYW5kbGVGdWxsc2NyZWVuVG9nZ2xlKCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnS2V5TSc6XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIGhhbmRsZU11dGVUb2dnbGUoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiDliJ3lp4vljJbmkq3mlL7lmahcbiAgICovXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZERyYW1hKGRyYW1hKTtcbiAgICBcbiAgICAvLyDmt7vliqDplK7nm5jkuovku7bnm5HlkKxcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bik7XG4gICAgXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICAgIGlmIChjb250cm9sc1RpbWVvdXQpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KGNvbnRyb2xzVGltZW91dCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW2RyYW1hXSk7XG5cbiAgLyoqXG4gICAqIOebkeWQrOWFqOWxj+eKtuaAgeWPmOWMllxuICAgKi9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVGdWxsc2NyZWVuQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgY29uc3QgaXNGdWxsc2NyZWVuID0gISFkb2N1bWVudC5mdWxsc2NyZWVuRWxlbWVudDtcbiAgICAgIGlmIChpc0Z1bGxzY3JlZW4gIT09IHBsYXllclN0YXRlLmlzRnVsbHNjcmVlbikge1xuICAgICAgICBpZiAoaXNGdWxsc2NyZWVuKSB7XG4gICAgICAgICAgZW50ZXJGdWxsc2NyZWVuKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZXhpdEZ1bGxzY3JlZW4oKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG5cbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdmdWxsc2NyZWVuY2hhbmdlJywgaGFuZGxlRnVsbHNjcmVlbkNoYW5nZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Z1bGxzY3JlZW5jaGFuZ2UnLCBoYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKTtcbiAgICB9O1xuICB9LCBbcGxheWVyU3RhdGUuaXNGdWxsc2NyZWVuXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICByZWY9e2NvbnRhaW5lclJlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICdyZWxhdGl2ZSBiZy1ibGFjayByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBncm91cCcsXG4gICAgICAgIHBsYXllclN0YXRlLmlzRnVsbHNjcmVlbiAmJiAnZml4ZWQgaW5zZXQtMCB6LTUwIHJvdW5kZWQtbm9uZScsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIG9uTW91c2VNb3ZlPXtoYW5kbGVNb3VzZU1vdmV9XG4gICAgICBvbk1vdXNlRW50ZXI9e2hhbmRsZU1vdXNlRW50ZXJ9XG4gICAgICBvbk1vdXNlTGVhdmU9e2hhbmRsZU1vdXNlTGVhdmV9XG4gICAgPlxuICAgICAgey8qIOinhumikeWFg+e0oCAqL31cbiAgICAgIDx2aWRlb1xuICAgICAgICByZWY9e3ZpZGVvUmVmfVxuICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb250YWluXCJcbiAgICAgICAgcG9zdGVyPXtwb3N0ZXIgfHwgZHJhbWEuY292ZXJ9XG4gICAgICAgIGF1dG9QbGF5PXthdXRvUGxheX1cbiAgICAgICAgcGxheXNJbmxpbmVcbiAgICAgICAgb25Mb2FkU3RhcnQ9e2hhbmRsZUxvYWRTdGFydH1cbiAgICAgICAgb25DYW5QbGF5PXtoYW5kbGVDYW5QbGF5fVxuICAgICAgICBvblRpbWVVcGRhdGU9e2hhbmRsZVRpbWVVcGRhdGV9XG4gICAgICAgIG9uRW5kZWQ9e2hhbmRsZUVuZGVkfVxuICAgICAgICBvbkVycm9yPXtoYW5kbGVFcnJvcn1cbiAgICAgICAgb25DbGljaz17aGFuZGxlUGxheVBhdXNlfVxuICAgICAgPlxuICAgICAgICB7dmlkZW9VcmwgJiYgPHNvdXJjZSBzcmM9e3ZpZGVvVXJsfSB0eXBlPVwidmlkZW8vbXA0XCIgLz59XG4gICAgICAgIOaCqOeahOa1j+iniOWZqOS4jeaUr+aMgeinhumikeaSreaUvuOAglxuICAgICAgPC92aWRlbz5cblxuICAgICAgey8qIOWKoOi9veaMh+ekuuWZqCAqL31cbiAgICAgIHtwbGF5ZXJTdGF0ZS5pc0xvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmxhY2sgYmctb3BhY2l0eS01MFwiPlxuICAgICAgICAgIDxMb2FkZXIgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtd2hpdGUgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog5pKt5pS+5oyJ6ZKu6KaG55uW5bGCICovfVxuICAgICAgeyFwbGF5ZXJTdGF0ZS5pc1BsYXlpbmcgJiYgIXBsYXllclN0YXRlLmlzTG9hZGluZyAmJiAoXG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOCB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmxhY2sgYmctb3BhY2l0eS0zMFwiXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlUGxheVBhdXNlfVxuICAgICAgICA+XG4gICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMSB9fVxuICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0yMCBoLTIwIGJnLXdoaXRlIGJnLW9wYWNpdHktOTAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFBsYXkgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWdyYXktOTAwIG1sLTFcIiAvPlxuICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIOaOp+WItuagjyAqL31cbiAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgIHtzaG93Q29udHJvbHMgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJsYWNrIHZpYS1ibGFjay84MCB0by10cmFuc3BhcmVudCBwLTRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiDov5vluqbmnaEgKi99XG4gICAgICAgICAgICA8UHJvZ3Jlc3NCYXJcbiAgICAgICAgICAgICAgY3VycmVudFRpbWU9e3BsYXllclN0YXRlLmN1cnJlbnRUaW1lfVxuICAgICAgICAgICAgICBkdXJhdGlvbj17cGxheWVyU3RhdGUuZHVyYXRpb259XG4gICAgICAgICAgICAgIG9uU2Vlaz17aGFuZGxlU2Vla31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItNFwiXG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICB7Lyog5o6n5Yi25oyJ6ZKuICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8UGxheWVyQ29udHJvbHNcbiAgICAgICAgICAgICAgICAgIGlzUGxheWluZz17cGxheWVyU3RhdGUuaXNQbGF5aW5nfVxuICAgICAgICAgICAgICAgICAgb25QbGF5UGF1c2U9e2hhbmRsZVBsYXlQYXVzZX1cbiAgICAgICAgICAgICAgICAgIG9uUmV3aW5kPXsoKSA9PiBoYW5kbGVTZWVrKE1hdGgubWF4KDAsIHBsYXllclN0YXRlLmN1cnJlbnRUaW1lIC0gMTApKX1cbiAgICAgICAgICAgICAgICAgIG9uRmFzdEZvcndhcmQ9eygpID0+IGhhbmRsZVNlZWsoTWF0aC5taW4ocGxheWVyU3RhdGUuZHVyYXRpb24sIHBsYXllclN0YXRlLmN1cnJlbnRUaW1lICsgMTApKX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPFZvbHVtZUNvbnRyb2xcbiAgICAgICAgICAgICAgICAgIHZvbHVtZT17cGxheWVyU3RhdGUudm9sdW1lfVxuICAgICAgICAgICAgICAgICAgb25Wb2x1bWVDaGFuZ2U9e2hhbmRsZVZvbHVtZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIG9uTXV0ZVRvZ2dsZT17aGFuZGxlTXV0ZVRvZ2dsZX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIHtNYXRoLmZsb29yKHBsYXllclN0YXRlLmN1cnJlbnRUaW1lIC8gNjApfTp7TWF0aC5mbG9vcihwbGF5ZXJTdGF0ZS5jdXJyZW50VGltZSAlIDYwKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9IC8ge01hdGguZmxvb3IocGxheWVyU3RhdGUuZHVyYXRpb24gLyA2MCl9OntNYXRoLmZsb29yKHBsYXllclN0YXRlLmR1cmF0aW9uICUgNjApLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93U2V0dGluZ3MoIXNob3dTZXR0aW5ncyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZSBob3ZlcjpiZy1vcGFjaXR5LTIwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUZ1bGxzY3JlZW5Ub2dnbGV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZSBob3ZlcjpiZy1vcGFjaXR5LTIwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtwbGF5ZXJTdGF0ZS5pc0Z1bGxzY3JlZW4gPyAoXG4gICAgICAgICAgICAgICAgICAgIDxNaW5pbWl6ZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxNYXhpbWl6ZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG5cbiAgICAgIHsvKiDorr7nva7oj5zljZUgKi99XG4gICAgICB7c2hvd1NldHRpbmdzICYmIChcbiAgICAgICAgPFNldHRpbmdzTWVudVxuICAgICAgICAgIHBsYXliYWNrUmF0ZT17cGxheWVyU3RhdGUucGxheWJhY2tSYXRlfVxuICAgICAgICAgIHF1YWxpdHk9e3BsYXllclN0YXRlLnF1YWxpdHl9XG4gICAgICAgICAgb25QbGF5YmFja1JhdGVDaGFuZ2U9e2hhbmRsZVBsYXliYWNrUmF0ZUNoYW5nZX1cbiAgICAgICAgICBvblF1YWxpdHlDaGFuZ2U9e3NldFF1YWxpdHl9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1NldHRpbmdzKGZhbHNlKX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDplJnor6/mj5DnpLogKi99XG4gICAgICB7cGxheWVyU3RhdGUuZXJyb3IgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmxhY2sgYmctb3BhY2l0eS04MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj7mkq3mlL7lh7rplJk8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktMzAwIG1iLTRcIj57cGxheWVyU3RhdGUuZXJyb3J9PC9kaXY+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEVycm9yKHVuZGVmaW5lZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1wcmltYXJ5LTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgaG92ZXI6YmctcHJpbWFyeS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDph43or5VcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIlBsYXkiLCJNYXhpbWl6ZSIsIk1pbmltaXplIiwiU2V0dGluZ3MiLCJMb2FkZXIiLCJQbGF5ZXJDb250cm9scyIsIlByb2dyZXNzQmFyIiwiVm9sdW1lQ29udHJvbCIsIlNldHRpbmdzTWVudSIsInVzZVBsYXllciIsImNuIiwiVmlkZW9QbGF5ZXIiLCJkcmFtYSIsInZpZGVvVXJsIiwicG9zdGVyIiwiYXV0b1BsYXkiLCJjbGFzc05hbWUiLCJvbkVuZGVkIiwib25FcnJvciIsInZpZGVvUmVmIiwiY29udGFpbmVyUmVmIiwic2hvd0NvbnRyb2xzIiwic2V0U2hvd0NvbnRyb2xzIiwic2hvd1NldHRpbmdzIiwic2V0U2hvd1NldHRpbmdzIiwiaXNIb3ZlcmluZyIsInNldElzSG92ZXJpbmciLCJjb250cm9sc1RpbWVvdXQiLCJzZXRDb250cm9sc1RpbWVvdXQiLCJwbGF5ZXJTdGF0ZSIsImN1cnJlbnREcmFtYSIsInBsYXkiLCJwYXVzZSIsInRvZ2dsZVBsYXkiLCJzZXRWb2x1bWUiLCJ0b2dnbGVNdXRlIiwic2Vla1RvIiwiZmFzdEZvcndhcmQiLCJyZXdpbmQiLCJzZXRQbGF5YmFja1JhdGUiLCJzZXRRdWFsaXR5IiwiZW50ZXJGdWxsc2NyZWVuIiwiZXhpdEZ1bGxzY3JlZW4iLCJ0b2dnbGVGdWxsc2NyZWVuIiwic2V0TG9hZGluZyIsInNldEVycm9yIiwidXBkYXRlQ3VycmVudFRpbWUiLCJ1cGRhdGVEdXJhdGlvbiIsImxvYWREcmFtYSIsImhpZGVDb250cm9sc1RpbWVyIiwiY2xlYXJUaW1lb3V0IiwidGltZW91dCIsInNldFRpbWVvdXQiLCJpc1BsYXlpbmciLCJzaG93Q29udHJvbHNIYW5kbGVyIiwiaGFuZGxlTW91c2VNb3ZlIiwiaGFuZGxlTW91c2VFbnRlciIsImhhbmRsZU1vdXNlTGVhdmUiLCJoYW5kbGVMb2FkU3RhcnQiLCJoYW5kbGVDYW5QbGF5IiwiY3VycmVudCIsImR1cmF0aW9uIiwiaGFuZGxlVGltZVVwZGF0ZSIsImN1cnJlbnRUaW1lIiwiaGFuZGxlRW5kZWQiLCJoYW5kbGVFcnJvciIsImVycm9yTWVzc2FnZSIsImhhbmRsZVBsYXlQYXVzZSIsImhhbmRsZVNlZWsiLCJ0aW1lIiwiaGFuZGxlVm9sdW1lQ2hhbmdlIiwidm9sdW1lIiwiaGFuZGxlTXV0ZVRvZ2dsZSIsIm5ld1ZvbHVtZSIsImhhbmRsZVBsYXliYWNrUmF0ZUNoYW5nZSIsInJhdGUiLCJwbGF5YmFja1JhdGUiLCJoYW5kbGVGdWxsc2NyZWVuVG9nZ2xlIiwiaXNGdWxsc2NyZWVuIiwiZG9jdW1lbnQiLCJyZXF1ZXN0RnVsbHNjcmVlbiIsImhhbmRsZUtleURvd24iLCJldmVudCIsImNvZGUiLCJwcmV2ZW50RGVmYXVsdCIsIk1hdGgiLCJtYXgiLCJtaW4iLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZUZ1bGxzY3JlZW5DaGFuZ2UiLCJmdWxsc2NyZWVuRWxlbWVudCIsImRpdiIsInJlZiIsIm9uTW91c2VNb3ZlIiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwidmlkZW8iLCJjb3ZlciIsInBsYXlzSW5saW5lIiwib25Mb2FkU3RhcnQiLCJvbkNhblBsYXkiLCJvblRpbWVVcGRhdGUiLCJvbkNsaWNrIiwic291cmNlIiwic3JjIiwidHlwZSIsImlzTG9hZGluZyIsImluaXRpYWwiLCJvcGFjaXR5Iiwic2NhbGUiLCJhbmltYXRlIiwiZXhpdCIsImJ1dHRvbiIsIndoaWxlSG92ZXIiLCJ3aGlsZVRhcCIsInkiLCJ0cmFuc2l0aW9uIiwib25TZWVrIiwib25QbGF5UGF1c2UiLCJvblJld2luZCIsIm9uRmFzdEZvcndhcmQiLCJvblZvbHVtZUNoYW5nZSIsIm9uTXV0ZVRvZ2dsZSIsImZsb29yIiwidG9TdHJpbmciLCJwYWRTdGFydCIsInF1YWxpdHkiLCJvblBsYXliYWNrUmF0ZUNoYW5nZSIsIm9uUXVhbGl0eUNoYW5nZSIsIm9uQ2xvc2UiLCJlcnJvciIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/player/VideoPlayer.tsx\n"));

/***/ }),

/***/ "./src/components/player/VolumeControl.tsx":
/*!*************************************************!*\
  !*** ./src/components/player/VolumeControl.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VolumeControl: function() { return /* binding */ VolumeControl; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Volume1_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Volume1,Volume2,VolumeX!=!lucide-react */ \"__barrel_optimize__?names=Volume1,Volume2,VolumeX!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n/**\n * 音量控制组件\n */ function VolumeControl({ volume, onVolumeChange, onMuteToggle, className }) {\n    _s();\n    const [showSlider, setShowSlider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /**\n   * 获取音量图标\n   */ const getVolumeIcon = ()=>{\n        if (volume === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume1_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__.VolumeX, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                lineNumber: 29,\n                columnNumber: 14\n            }, this);\n        } else if (volume < 0.5) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume1_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Volume1, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                lineNumber: 31,\n                columnNumber: 14\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume1_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Volume2, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                lineNumber: 33,\n                columnNumber: 14\n            }, this);\n        }\n    };\n    /**\n   * 处理音量滑块变化\n   */ const handleSliderChange = (event)=>{\n        const newVolume = parseFloat(event.target.value);\n        onVolumeChange(newVolume);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex items-center\", className),\n        onMouseEnter: ()=>setShowSlider(true),\n        onMouseLeave: ()=>setShowSlider(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: onMuteToggle,\n                className: \"p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors\",\n                \"aria-label\": volume === 0 ? \"取消静音\" : \"静音\",\n                children: getVolumeIcon()\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: showSlider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: -10\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"absolute left-full ml-2 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-20 h-1 bg-white bg-opacity-30 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 h-full bg-white rounded-full transition-all duration-200\",\n                                    style: {\n                                        width: `${volume * 100}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"0\",\n                                    max: \"1\",\n                                    step: \"0.01\",\n                                    value: volume,\n                                    onChange: handleSliderChange,\n                                    className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer\",\n                                    \"aria-label\": \"音量\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 w-3 h-3 bg-white rounded-full transform -translate-y-1/2 pointer-events-none\",\n                                    style: {\n                                        left: `${volume * 100}%`,\n                                        marginLeft: \"-6px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-white text-xs min-w-[2rem]\",\n                            children: [\n                                Math.round(volume * 100),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\components\\\\player\\\\VolumeControl.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(VolumeControl, \"qukcKQWwt/hPpAKgu5QWbkh2A6s=\");\n_c = VolumeControl;\nvar _c;\n$RefreshReg$(_c, \"VolumeControl\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wbGF5ZXIvVm9sdW1lQ29udHJvbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNnQjtBQUNDO0FBQzVCO0FBUzdCOztDQUVDLEdBQ00sU0FBU1EsY0FBYyxFQUM1QkMsTUFBTSxFQUNOQyxjQUFjLEVBQ2RDLFlBQVksRUFDWkMsU0FBUyxFQUNVOztJQUNuQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFFN0M7O0dBRUMsR0FDRCxNQUFNYyxnQkFBZ0I7UUFDcEIsSUFBSU4sV0FBVyxHQUFHO1lBQ2hCLHFCQUFPLDhEQUFDSixnR0FBT0E7Z0JBQUNPLFdBQVU7Ozs7OztRQUM1QixPQUFPLElBQUlILFNBQVMsS0FBSztZQUN2QixxQkFBTyw4REFBQ0gsZ0dBQU9BO2dCQUFDTSxXQUFVOzs7Ozs7UUFDNUIsT0FBTztZQUNMLHFCQUFPLDhEQUFDUixnR0FBT0E7Z0JBQUNRLFdBQVU7Ozs7OztRQUM1QjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNSSxxQkFBcUIsQ0FBQ0M7UUFDMUIsTUFBTUMsWUFBWUMsV0FBV0YsTUFBTUcsTUFBTSxDQUFDQyxLQUFLO1FBQy9DWCxlQUFlUTtJQUNqQjtJQUVBLHFCQUNFLDhEQUFDSTtRQUNDVixXQUFXTCwwQ0FBRUEsQ0FBQyw4QkFBOEJLO1FBQzVDVyxjQUFjLElBQU1ULGNBQWM7UUFDbENVLGNBQWMsSUFBTVYsY0FBYzs7MEJBR2xDLDhEQUFDWixpREFBTUEsQ0FBQ3VCLE1BQU07Z0JBQ1pDLFlBQVk7b0JBQUVDLE9BQU87Z0JBQUk7Z0JBQ3pCQyxVQUFVO29CQUFFRCxPQUFPO2dCQUFJO2dCQUN2QkUsU0FBU2xCO2dCQUNUQyxXQUFVO2dCQUNWa0IsY0FBWXJCLFdBQVcsSUFBSSxTQUFTOzBCQUVuQ007Ozs7OzswQkFJSCw4REFBQ1osMERBQWVBOzBCQUNiVSw0QkFDQyw4REFBQ1gsaURBQU1BLENBQUNvQixHQUFHO29CQUNUUyxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHQyxHQUFHLENBQUM7b0JBQUc7b0JBQzlCQyxTQUFTO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUM1QkUsTUFBTTt3QkFBRUgsU0FBUzt3QkFBR0MsR0FBRyxDQUFDO29CQUFHO29CQUMzQkcsWUFBWTt3QkFBRUMsVUFBVTtvQkFBSTtvQkFDNUJ6QixXQUFVOztzQ0FFViw4REFBQ1U7NEJBQUlWLFdBQVU7OzhDQUViLDhEQUFDVTtvQ0FDQ1YsV0FBVTtvQ0FDVjBCLE9BQU87d0NBQUVDLE9BQU8sQ0FBQyxFQUFFOUIsU0FBUyxJQUFJLENBQUMsQ0FBQztvQ0FBQzs7Ozs7OzhDQUlyQyw4REFBQytCO29DQUNDQyxNQUFLO29DQUNMQyxLQUFJO29DQUNKQyxLQUFJO29DQUNKQyxNQUFLO29DQUNMdkIsT0FBT1o7b0NBQ1BvQyxVQUFVN0I7b0NBQ1ZKLFdBQVU7b0NBQ1ZrQixjQUFXOzs7Ozs7OENBSWIsOERBQUNSO29DQUNDVixXQUFVO29DQUNWMEIsT0FBTzt3Q0FBRVEsTUFBTSxDQUFDLEVBQUVyQyxTQUFTLElBQUksQ0FBQyxDQUFDO3dDQUFFc0MsWUFBWTtvQ0FBTzs7Ozs7Ozs7Ozs7O3NDQUsxRCw4REFBQ0M7NEJBQUtwQyxXQUFVOztnQ0FDYnFDLEtBQUtDLEtBQUssQ0FBQ3pDLFNBQVM7Z0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU94QztHQTNGZ0JEO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3BsYXllci9Wb2x1bWVDb250cm9sLnRzeD83ZjEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBWb2x1bWUyLCBWb2x1bWVYLCBWb2x1bWUxIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC91dGlscyc7XG5cbmludGVyZmFjZSBWb2x1bWVDb250cm9sUHJvcHMge1xuICB2b2x1bWU6IG51bWJlcjtcbiAgb25Wb2x1bWVDaGFuZ2U6ICh2b2x1bWU6IG51bWJlcikgPT4gdm9pZDtcbiAgb25NdXRlVG9nZ2xlOiAoKSA9PiB2b2lkO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbi8qKlxuICog6Z+z6YeP5o6n5Yi257uE5Lu2XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBWb2x1bWVDb250cm9sKHtcbiAgdm9sdW1lLFxuICBvblZvbHVtZUNoYW5nZSxcbiAgb25NdXRlVG9nZ2xlLFxuICBjbGFzc05hbWUsXG59OiBWb2x1bWVDb250cm9sUHJvcHMpIHtcbiAgY29uc3QgW3Nob3dTbGlkZXIsIHNldFNob3dTbGlkZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8qKlxuICAgKiDojrflj5bpn7Pph4/lm77moIdcbiAgICovXG4gIGNvbnN0IGdldFZvbHVtZUljb24gPSAoKSA9PiB7XG4gICAgaWYgKHZvbHVtZSA9PT0gMCkge1xuICAgICAgcmV0dXJuIDxWb2x1bWVYIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPjtcbiAgICB9IGVsc2UgaWYgKHZvbHVtZSA8IDAuNSkge1xuICAgICAgcmV0dXJuIDxWb2x1bWUxIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPjtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIDxWb2x1bWUyIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgLyoqXG4gICAqIOWkhOeQhumfs+mHj+a7keWdl+WPmOWMllxuICAgKi9cbiAgY29uc3QgaGFuZGxlU2xpZGVyQ2hhbmdlID0gKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IG5ld1ZvbHVtZSA9IHBhcnNlRmxvYXQoZXZlbnQudGFyZ2V0LnZhbHVlKTtcbiAgICBvblZvbHVtZUNoYW5nZShuZXdWb2x1bWUpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBcbiAgICAgIGNsYXNzTmFtZT17Y24oJ3JlbGF0aXZlIGZsZXggaXRlbXMtY2VudGVyJywgY2xhc3NOYW1lKX1cbiAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gc2V0U2hvd1NsaWRlcih0cnVlKX1cbiAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0U2hvd1NsaWRlcihmYWxzZSl9XG4gICAgPlxuICAgICAgey8qIOmfs+mHj+aMiemSriAqL31cbiAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMSB9fVxuICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45IH19XG4gICAgICAgIG9uQ2xpY2s9e29uTXV0ZVRvZ2dsZX1cbiAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUgaG92ZXI6Ymctb3BhY2l0eS0yMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICBhcmlhLWxhYmVsPXt2b2x1bWUgPT09IDAgPyAn5Y+W5raI6Z2Z6Z+zJyA6ICfpnZnpn7MnfVxuICAgICAgPlxuICAgICAgICB7Z2V0Vm9sdW1lSWNvbigpfVxuICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuXG4gICAgICB7Lyog6Z+z6YeP5ruR5Z2XICovfVxuICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAge3Nob3dTbGlkZXIgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0xMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC0xMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4yIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LWZ1bGwgbWwtMiBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTIwIGgtMSBiZy13aGl0ZSBiZy1vcGFjaXR5LTMwIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICB7Lyog6Z+z6YeP6L+b5bqmICovfVxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIGgtZnVsbCBiZy13aGl0ZSByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7dm9sdW1lICogMTAwfSVgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7Lyog6Z+z6YeP5ruR5Z2XICovfVxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwicmFuZ2VcIlxuICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgIG1heD1cIjFcIlxuICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17dm9sdW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVTbGlkZXJDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LWZ1bGwgaC1mdWxsIG9wYWNpdHktMCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIumfs+mHj1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7Lyog5ruR5Z2X5oyH56S65ZmoICovfVxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEvMiB3LTMgaC0zIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBwb2ludGVyLWV2ZW50cy1ub25lXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBsZWZ0OiBgJHt2b2x1bWUgKiAxMDB9JWAsIG1hcmdpbkxlZnQ6ICctNnB4JyB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiDpn7Pph4/nmb7liIbmr5QgKi99XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtd2hpdGUgdGV4dC14cyBtaW4tdy1bMnJlbV1cIj5cbiAgICAgICAgICAgICAge01hdGgucm91bmQodm9sdW1lICogMTAwKX0lXG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIlZvbHVtZTIiLCJWb2x1bWVYIiwiVm9sdW1lMSIsImNuIiwiVm9sdW1lQ29udHJvbCIsInZvbHVtZSIsIm9uVm9sdW1lQ2hhbmdlIiwib25NdXRlVG9nZ2xlIiwiY2xhc3NOYW1lIiwic2hvd1NsaWRlciIsInNldFNob3dTbGlkZXIiLCJnZXRWb2x1bWVJY29uIiwiaGFuZGxlU2xpZGVyQ2hhbmdlIiwiZXZlbnQiLCJuZXdWb2x1bWUiLCJwYXJzZUZsb2F0IiwidGFyZ2V0IiwidmFsdWUiLCJkaXYiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJidXR0b24iLCJ3aGlsZUhvdmVyIiwic2NhbGUiLCJ3aGlsZVRhcCIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ4IiwiYW5pbWF0ZSIsImV4aXQiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJzdHlsZSIsIndpZHRoIiwiaW5wdXQiLCJ0eXBlIiwibWluIiwibWF4Iiwic3RlcCIsIm9uQ2hhbmdlIiwibGVmdCIsIm1hcmdpbkxlZnQiLCJzcGFuIiwiTWF0aCIsInJvdW5kIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/player/VolumeControl.tsx\n"));

/***/ }),

/***/ "./src/hooks/usePlayer.ts":
/*!********************************!*\
  !*** ./src/hooks/usePlayer.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePlayer: function() { return /* binding */ usePlayer; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store */ \"./src/store/index.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"./src/utils/index.ts\");\n\n\n\n/**\n * 视频播放器功能hook\n */ function usePlayer() {\n    const { playerState, currentDrama, setPlayerState, setCurrentDrama, resetPlayer } = (0,_store__WEBPACK_IMPORTED_MODULE_1__.usePlayerStore)();\n    const { addWatchHistory, updateWatchProgress } = (0,_store__WEBPACK_IMPORTED_MODULE_1__.useWatchHistoryStore)();\n    const { preferences } = (0,_store__WEBPACK_IMPORTED_MODULE_1__.usePreferencesStore)();\n    const progressUpdateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    /**\n   * 播放视频\n   */ const play = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setPlayerState({\n            isPlaying: true\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 暂停视频\n   */ const pause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setPlayerState({\n            isPlaying: false\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 切换播放/暂停\n   */ const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (playerState.isPlaying) {\n            pause();\n        } else {\n            play();\n        }\n    }, [\n        playerState.isPlaying,\n        play,\n        pause\n    ]);\n    /**\n   * 设置音量\n   */ const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((volume)=>{\n        const clampedVolume = Math.max(0, Math.min(1, volume));\n        setPlayerState({\n            volume: clampedVolume\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 切换静音\n   */ const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const newVolume = playerState.volume > 0 ? 0 : preferences.volume;\n        setVolume(newVolume);\n    }, [\n        playerState.volume,\n        preferences.volume,\n        setVolume\n    ]);\n    /**\n   * 设置播放进度\n   */ const seekTo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((time)=>{\n        const clampedTime = Math.max(0, Math.min(playerState.duration, time));\n        setPlayerState({\n            currentTime: clampedTime\n        });\n    }, [\n        playerState.duration,\n        setPlayerState\n    ]);\n    /**\n   * 快进\n   */ const fastForward = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((seconds = 10)=>{\n        seekTo(playerState.currentTime + seconds);\n    }, [\n        playerState.currentTime,\n        seekTo\n    ]);\n    /**\n   * 快退\n   */ const rewind = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((seconds = 10)=>{\n        seekTo(playerState.currentTime - seconds);\n    }, [\n        playerState.currentTime,\n        seekTo\n    ]);\n    /**\n   * 设置播放速度\n   */ const setPlaybackRate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((rate)=>{\n        setPlayerState({\n            playbackRate: rate\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 设置视频质量\n   */ const setQuality = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((quality)=>{\n        setPlayerState({\n            quality\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 进入全屏\n   */ const enterFullscreen = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setPlayerState({\n            isFullscreen: true\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 退出全屏\n   */ const exitFullscreen = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setPlayerState({\n            isFullscreen: false\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 切换全屏\n   */ const toggleFullscreen = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (playerState.isFullscreen) {\n            exitFullscreen();\n        } else {\n            enterFullscreen();\n        }\n    }, [\n        playerState.isFullscreen,\n        enterFullscreen,\n        exitFullscreen\n    ]);\n    /**\n   * 设置加载状态\n   */ const setLoading = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((isLoading)=>{\n        setPlayerState({\n            isLoading\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 设置错误状态\n   */ const setError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((error)=>{\n        setPlayerState({\n            error\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 更新播放时间\n   */ const updateCurrentTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((currentTime)=>{\n        setPlayerState({\n            currentTime\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 更新视频时长\n   */ const updateDuration = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((duration)=>{\n        setPlayerState({\n            duration\n        });\n    }, [\n        setPlayerState\n    ]);\n    /**\n   * 加载新视频\n   */ const loadDrama = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((drama, startTime = 0)=>{\n        setCurrentDrama(drama);\n        setPlayerState({\n            currentTime: startTime,\n            duration: 0,\n            isLoading: true,\n            error: undefined\n        });\n        // 添加到观看历史\n        addWatchHistory(drama, startTime / 3600); // 假设视频时长为1小时\n    }, [\n        setCurrentDrama,\n        setPlayerState,\n        addWatchHistory\n    ]);\n    /**\n   * 节流的进度更新函数\n   */ const throttledProgressUpdate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((0,_utils__WEBPACK_IMPORTED_MODULE_2__.throttle)((dramaId, progress)=>{\n        updateWatchProgress(dramaId, progress);\n    }, 5000), [\n        updateWatchProgress\n    ]);\n    /**\n   * 处理播放进度更新\n   */ const handleProgressUpdate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!currentDrama || playerState.duration === 0) return;\n        const progress = playerState.currentTime / playerState.duration;\n        throttledProgressUpdate(currentDrama.book_id, progress);\n    }, [\n        currentDrama,\n        playerState.currentTime,\n        playerState.duration,\n        throttledProgressUpdate\n    ]);\n    /**\n   * 获取播放进度百分比\n   */ const getProgressPercentage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (playerState.duration === 0) return 0;\n        return playerState.currentTime / playerState.duration * 100;\n    }, [\n        playerState.currentTime,\n        playerState.duration\n    ]);\n    /**\n   * 获取缓冲进度百分比\n   */ const getBufferedPercentage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        // 这里需要根据实际的视频播放器实现\n        // 暂时返回一个模拟值\n        return Math.min(getProgressPercentage() + 10, 100);\n    }, [\n        getProgressPercentage\n    ]);\n    /**\n   * 键盘快捷键处理\n   */ const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event)=>{\n        if (!currentDrama) return;\n        switch(event.code){\n            case \"Space\":\n                event.preventDefault();\n                togglePlay();\n                break;\n            case \"ArrowLeft\":\n                event.preventDefault();\n                rewind(10);\n                break;\n            case \"ArrowRight\":\n                event.preventDefault();\n                fastForward(10);\n                break;\n            case \"ArrowUp\":\n                event.preventDefault();\n                setVolume(Math.min(1, playerState.volume + 0.1));\n                break;\n            case \"ArrowDown\":\n                event.preventDefault();\n                setVolume(Math.max(0, playerState.volume - 0.1));\n                break;\n            case \"KeyF\":\n                event.preventDefault();\n                toggleFullscreen();\n                break;\n            case \"KeyM\":\n                event.preventDefault();\n                toggleMute();\n                break;\n        }\n    }, [\n        currentDrama,\n        togglePlay,\n        rewind,\n        fastForward,\n        setVolume,\n        playerState.volume,\n        toggleFullscreen,\n        toggleMute\n    ]);\n    /**\n   * 监听播放进度变化\n   */ (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (playerState.isPlaying && currentDrama) {\n            handleProgressUpdate();\n        }\n    }, [\n        playerState.isPlaying,\n        playerState.currentTime,\n        currentDrama,\n        handleProgressUpdate\n    ]);\n    /**\n   * 监听键盘事件\n   */ (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        document.addEventListener(\"keydown\", handleKeyPress);\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyPress);\n        };\n    }, [\n        handleKeyPress\n    ]);\n    /**\n   * 清理定时器\n   */ (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (progressUpdateRef.current) {\n                clearTimeout(progressUpdateRef.current);\n            }\n        };\n    }, []);\n    return {\n        // 状态\n        playerState,\n        currentDrama,\n        progressPercentage: getProgressPercentage(),\n        bufferedPercentage: getBufferedPercentage(),\n        // 播放控制\n        play,\n        pause,\n        togglePlay,\n        // 音量控制\n        setVolume,\n        toggleMute,\n        // 进度控制\n        seekTo,\n        fastForward,\n        rewind,\n        // 播放设置\n        setPlaybackRate,\n        setQuality,\n        // 全屏控制\n        enterFullscreen,\n        exitFullscreen,\n        toggleFullscreen,\n        // 状态更新\n        setLoading,\n        setError,\n        updateCurrentTime,\n        updateDuration,\n        // 视频加载\n        loadDrama,\n        resetPlayer\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/usePlayer.ts\n"));

/***/ }),

/***/ "./src/hooks/useWatchHistory.ts":
/*!**************************************!*\
  !*** ./src/hooks/useWatchHistory.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWatchHistory: function() { return /* binding */ useWatchHistory; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store */ \"./src/store/index.ts\");\n\n\n/**\n * 观看历史功能hook\n */ function useWatchHistory() {\n    const { watchHistory, addWatchHistory, updateWatchProgress, removeWatchHistory, clearWatchHistory, getWatchHistory } = (0,_store__WEBPACK_IMPORTED_MODULE_1__.useWatchHistoryStore)();\n    /**\n   * 添加观看记录\n   */ const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((drama, progress = 0, episode = 1)=>{\n        addWatchHistory(drama, progress, episode);\n    }, [\n        addWatchHistory\n    ]);\n    /**\n   * 更新观看进度\n   */ const updateProgress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dramaId, progress, episode)=>{\n        updateWatchProgress(dramaId, progress, episode);\n    }, [\n        updateWatchProgress\n    ]);\n    /**\n   * 删除观看记录\n   */ const removeFromHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id)=>{\n        removeWatchHistory(id);\n    }, [\n        removeWatchHistory\n    ]);\n    /**\n   * 清空观看历史\n   */ const clearAllHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        clearWatchHistory();\n    }, [\n        clearWatchHistory\n    ]);\n    /**\n   * 获取特定短剧的观看记录\n   */ const getDramaHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dramaId)=>{\n        return getWatchHistory(dramaId);\n    }, [\n        getWatchHistory\n    ]);\n    /**\n   * 检查是否已观看过\n   */ const hasWatched = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dramaId)=>{\n        return watchHistory.some((item)=>item.drama.book_id === dramaId);\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 获取继续观看列表\n   */ const continueWatching = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        return watchHistory.filter((item)=>item.progress > 0 && item.progress < 0.9) // 进度在0-90%之间\n        .slice(0, 10); // 最多显示10个\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 获取最近观看列表\n   */ const recentlyWatched = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        return watchHistory.sort((a, b)=>b.watchedAt - a.watchedAt).slice(0, 20); // 最多显示20个\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 获取已完成观看列表\n   */ const completedWatching = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        return watchHistory.filter((item)=>item.progress >= 0.9) // 进度大于等于90%\n        .sort((a, b)=>b.watchedAt - a.watchedAt);\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 获取观看统计信息\n   */ const watchStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalWatched = watchHistory.length;\n        const totalCompleted = completedWatching.length;\n        const totalInProgress = continueWatching.length;\n        const completionRate = totalWatched > 0 ? totalCompleted / totalWatched * 100 : 0;\n        // 计算总观看时长（估算）\n        const totalWatchTime = watchHistory.reduce((total, item)=>{\n            // 假设每部短剧平均60分钟\n            const estimatedDuration = 60 * 60; // 60分钟转换为秒\n            return total + estimatedDuration * item.progress;\n        }, 0);\n        // 获取最喜欢的类型\n        const categoryCount = {};\n        watchHistory.forEach((item)=>{\n            const categories = item.drama.category_schema.split(\"、\");\n            categories.forEach((category)=>{\n                categoryCount[category] = (categoryCount[category] || 0) + 1;\n            });\n        });\n        const favoriteCategories = Object.entries(categoryCount).sort(([, a], [, b])=>b - a).slice(0, 5).map(([category, count])=>({\n                category,\n                count\n            }));\n        return {\n            totalWatched,\n            totalCompleted,\n            totalInProgress,\n            completionRate: Math.round(completionRate),\n            totalWatchTime: Math.round(totalWatchTime),\n            favoriteCategories\n        };\n    }, [\n        watchHistory,\n        completedWatching,\n        continueWatching\n    ]);\n    /**\n   * 按日期分组的观看历史\n   */ const groupedByDate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const groups = {};\n        watchHistory.forEach((item)=>{\n            const date = new Date(item.watchedAt);\n            const dateKey = date.toDateString();\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push(item);\n        });\n        // 按日期排序\n        const sortedGroups = Object.entries(groups).sort(([a], [b])=>new Date(b).getTime() - new Date(a).getTime()).map(([date, items])=>({\n                date,\n                items: items.sort((a, b)=>b.watchedAt - a.watchedAt)\n            }));\n        return sortedGroups;\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 搜索观看历史\n   */ const searchHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((keyword)=>{\n        if (!keyword.trim()) return watchHistory;\n        const lowerKeyword = keyword.toLowerCase();\n        return watchHistory.filter((item)=>item.drama.title.toLowerCase().includes(lowerKeyword) || item.drama.author.toLowerCase().includes(lowerKeyword) || item.drama.category_schema.toLowerCase().includes(lowerKeyword));\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 按类型筛选观看历史\n   */ const filterByCategory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((category)=>{\n        if (category === \"all\") return watchHistory;\n        return watchHistory.filter((item)=>item.drama.category_schema.includes(category));\n    }, [\n        watchHistory\n    ]);\n    /**\n   * 导出观看历史数据\n   */ const exportHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const data = {\n            exportDate: new Date().toISOString(),\n            totalItems: watchHistory.length,\n            history: watchHistory.map((item)=>({\n                    title: item.drama.title,\n                    author: item.drama.author,\n                    category: item.drama.category_schema,\n                    progress: item.progress,\n                    episode: item.episode,\n                    watchedAt: new Date(item.watchedAt).toISOString()\n                }))\n        };\n        const blob = new Blob([\n            JSON.stringify(data, null, 2)\n        ], {\n            type: \"application/json\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `watch-history-${new Date().toISOString().split(\"T\")[0]}.json`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    }, [\n        watchHistory\n    ]);\n    return {\n        // 状态\n        watchHistory,\n        continueWatching,\n        recentlyWatched,\n        completedWatching,\n        watchStats,\n        groupedByDate,\n        // 操作\n        addToHistory,\n        updateProgress,\n        removeFromHistory,\n        clearAllHistory,\n        getDramaHistory,\n        hasWatched,\n        searchHistory,\n        filterByCategory,\n        exportHistory\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useWatchHistory.ts\n"));

/***/ }),

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_player_VideoPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/player/VideoPlayer */ \"./src/components/player/VideoPlayer.tsx\");\n/* harmony import */ var _components_player_QualitySelector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/player/QualitySelector */ \"./src/components/player/QualitySelector.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _hooks_useWatchHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useWatchHistory */ \"./src/hooks/useWatchHistory.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"./src/lib/api.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [relatedDramas, setRelatedDramas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPlayer, setShowPlayer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorited, setIsFavorited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"auto\");\n    const [showAutoPlayNotification, setShowAutoPlayNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoPlayCountdown, setAutoPlayCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [episodes, setEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [videoQualities, setVideoQualities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { addToHistory, getDramaHistory, hasWatched } = (0,_hooks_useWatchHistory__WEBPACK_IMPORTED_MODULE_9__.useWatchHistory)();\n    /**\n   * 生成模拟集数数据\n   */ const generateMockEpisodes = (totalEpisodes)=>{\n        return Array.from({\n            length: totalEpisodes\n        }, (_, index)=>({\n                id: `episode-${index + 1}`,\n                episodeNumber: index + 1,\n                title: `第${index + 1}集`,\n                duration: 300 + Math.random() * 600,\n                videoUrl: `https://example.com/videos/drama-${id}/episode-${index + 1}.mp4`,\n                thumbnailUrl: `https://example.com/thumbnails/drama-${id}/episode-${index + 1}.jpg`,\n                isWatched: Math.random() > 0.7,\n                watchProgress: Math.random() > 0.5 ? Math.random() : 0\n            }));\n    };\n    /**\n   * 生成模拟画质选项\n   */ const generateMockQualities = ()=>{\n        return [\n            {\n                label: \"自动\",\n                value: \"auto\"\n            },\n            {\n                label: \"1080P\",\n                value: \"1080p\",\n                bitrate: 2000\n            },\n            {\n                label: \"720P\",\n                value: \"720p\",\n                bitrate: 1200\n            },\n            {\n                label: \"480P\",\n                value: \"480p\",\n                bitrate: 800\n            }\n        ];\n    };\n    /**\n   * 获取短剧详情\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const dramaData = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.getDramaDetail(dramaId);\n            // 扩展短剧数据\n            const totalEpisodes = Math.floor(Math.random() * 50) + 10; // 10-60集\n            const extendedDrama = {\n                ...dramaData,\n                totalEpisodes,\n                rating: 4.2 + Math.random() * 0.6,\n                releaseDate: \"2024-01-15\",\n                tags: dramaData.category_schema.split(\"、\"),\n                currentEpisode: 1\n            };\n            setDrama(extendedDrama);\n            setEpisodes(generateMockEpisodes(totalEpisodes));\n            setVideoQualities(generateMockQualities());\n            // 获取相关推荐\n            const related = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.getRecommendedDramas(8);\n            setRelatedDramas(related.filter((item)=>item.book_id !== dramaId));\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode: episodeNumber\n            });\n        }\n    }, [\n        drama,\n        addToHistory\n    ]);\n    /**\n   * 处理播放\n   */ const handlePlay = ()=>{\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode\n            });\n            setShowPlayer(true);\n        }\n    };\n    /**\n   * 处理收藏\n   */ const handleFavorite = ()=>{\n        setIsFavorited(!isFavorited);\n    };\n    /**\n   * 处理分享\n   */ const handleShare = async ()=>{\n        if (!drama) return;\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: drama.title,\n                    text: drama.desc,\n                    url: window.location.href\n                });\n            } catch (err) {\n                console.log(\"分享取消\");\n            }\n        } else {\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_8__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_7__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_8__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    const watchHistory = getDramaHistory(drama.book_id);\n    const isWatched = hasWatched(drama.book_id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: `${drama.title},${drama.author},${drama.category_schema},短剧`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: drama.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: drama.cover\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"video.movie\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_8__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"bg-white rounded-xl shadow-soft overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-video bg-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_player_VideoPlayer__WEBPACK_IMPORTED_MODULE_4__.VideoPlayer, {\n                                                drama: drama,\n                                                videoUrl: episodes[currentEpisode - 1]?.videoUrl,\n                                                onEnded: ()=>{},\n                                                onError: (error)=>console.error(\"播放错误:\", error),\n                                                className: \"w-full h-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 left-4 z-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: drama.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: [\n                                                                \"第\",\n                                                                currentEpisode,\n                                                                \"集 / 共\",\n                                                                drama.totalEpisodes,\n                                                                \"集\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 right-4 z-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_player_QualitySelector__WEBPACK_IMPORTED_MODULE_5__.QualityIndicator, {\n                                                    quality: currentQuality\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"gV0cVYym46QJYZNmXvhLREmNYU8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useWatchHistory__WEBPACK_IMPORTED_MODULE_9__.useWatchHistory\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCursor%20Project%5C%E7%9F%AD%E5%89%A7%5Csrc%5Cpages%5Cdrama%5C%5Bid%5D.tsx&page=%2Fdrama%2F%5Bid%5D!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);