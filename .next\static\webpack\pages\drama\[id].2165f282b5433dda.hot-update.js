"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/drama/[id]",{

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"./src/lib/api.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件 - 简化版本\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"720p\");\n    const [totalEpisodes, setTotalEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(80);\n    /**\n   * 画质选项\n   */ const qualityOptions = [\n        {\n            label: \"高清 720p\",\n            value: \"720p\"\n        },\n        {\n            label: \"超清 1080p\",\n            value: \"1080p\"\n        },\n        {\n            label: \"原始画质\",\n            value: \"original\"\n        }\n    ];\n    /**\n   * 获取短剧详情\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const dramaData = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getDramaDetail(dramaId);\n            // 扩展短剧数据\n            const totalEpisodes = Math.floor(Math.random() * 50) + 10; // 10-60集\n            const extendedDrama = {\n                ...dramaData,\n                totalEpisodes,\n                rating: 4.2 + Math.random() * 0.6,\n                releaseDate: \"2024-01-15\",\n                tags: dramaData.category_schema.split(\"、\"),\n                currentEpisode: 1\n            };\n            setDrama(extendedDrama);\n            setEpisodes(generateMockEpisodes(totalEpisodes));\n            setVideoQualities(generateMockQualities());\n            // 获取相关推荐\n            const related = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getRecommendedDramas(8);\n            setRelatedDramas(related.filter((item)=>item.book_id !== dramaId));\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = useCallback((episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode: episodeNumber\n            });\n        }\n    }, [\n        drama,\n        addToHistory\n    ]);\n    /**\n   * 处理播放\n   */ const handlePlay = ()=>{\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode\n            });\n            setShowPlayer(true);\n        }\n    };\n    /**\n   * 处理收藏\n   */ const handleFavorite = ()=>{\n        setIsFavorited(!isFavorited);\n    };\n    /**\n   * 处理分享\n   */ const handleShare = async ()=>{\n        if (!drama) return;\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: drama.title,\n                    text: drama.desc,\n                    url: window.location.href\n                });\n            } catch (err) {\n                console.log(\"分享取消\");\n            }\n        } else {\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    const watchHistory = getDramaHistory(drama.book_id);\n    const isWatched = hasWatched(drama.book_id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: `${drama.title},${drama.author},${drama.category_schema},短剧`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: drama.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: drama.cover\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"video.movie\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"bg-white rounded-xl shadow-soft overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-video bg-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlayer, {\n                                                        drama: drama,\n                                                        videoUrl: episodes[currentEpisode - 1]?.videoUrl,\n                                                        onEnded: ()=>{},\n                                                        onError: (error)=>console.error(\"播放错误:\", error),\n                                                        className: \"w-full h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: drama.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-300\",\n                                                                    children: [\n                                                                        \"第\",\n                                                                        currentEpisode,\n                                                                        \"集 / 共\",\n                                                                        drama.totalEpisodes,\n                                                                        \"集\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualityIndicator, {\n                                                            quality: currentQuality\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeControls, {\n                                                            currentEpisode: currentEpisode,\n                                                            totalEpisodes: drama.totalEpisodes || 1,\n                                                            onPreviousEpisode: ()=>{\n                                                                if (currentEpisode > 1) {\n                                                                    handleEpisodeSelect(currentEpisode - 1);\n                                                                }\n                                                            },\n                                                            onNextEpisode: ()=>{\n                                                                if (drama && currentEpisode < drama.totalEpisodes) {\n                                                                    handleEpisodeSelect(currentEpisode + 1);\n                                                                }\n                                                            },\n                                                            onReplay: ()=>{\n                                                                if (drama) {\n                                                                    addToHistory({\n                                                                        ...drama,\n                                                                        currentEpisode\n                                                                    });\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualitySelector, {\n                                                            qualities: videoQualities,\n                                                            currentQuality: currentQuality,\n                                                            onQualityChange: (quality)=>{\n                                                                setCurrentQuality(quality);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeList, {\n                                            episodes: episodes,\n                                            currentEpisode: currentEpisode,\n                                            onEpisodeSelect: handleEpisodeSelect\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"sb5x2aVRQvpsalUXhhTAsJ9MuWE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

});