import React from 'react';
import { motion } from 'framer-motion';
import { Play, Check } from 'lucide-react';
import { cn } from '@/utils';
import type { Episode } from '@/types';

interface EpisodeListProps {
  episodes: Episode[];
  currentEpisode: number;
  onEpisodeSelect: (episodeNumber: number) => void;
  className?: string;
}

/**
 * 剧集列表组件
 */
export function EpisodeList({
  episodes,
  currentEpisode,
  onEpisodeSelect,
  className,
}: EpisodeListProps) {
  return (
    <div className={cn('bg-white rounded-lg shadow-sm', className)}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          剧集列表 ({episodes.length}集)
        </h3>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-2">
          {episodes.map((episode, index) => (
            <motion.button
              key={episode.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: index * 0.02 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onEpisodeSelect(episode.episodeNumber)}
              className={cn(
                'relative aspect-square rounded-lg border-2 transition-all duration-200',
                'flex items-center justify-center text-sm font-medium',
                currentEpisode === episode.episodeNumber
                  ? 'border-primary-500 bg-primary-500 text-white shadow-lg'
                  : episode.isWatched
                  ? 'border-green-200 bg-green-50 text-green-700 hover:border-green-300'
                  : 'border-gray-200 bg-gray-50 text-gray-700 hover:border-gray-300 hover:bg-gray-100'
              )}
            >
              {/* 集数显示 */}
              <span className="relative z-10">
                第{episode.episodeNumber}集
              </span>
              
              {/* 播放图标 */}
              {currentEpisode === episode.episodeNumber && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Play className="w-4 h-4 text-white/80" />
                </div>
              )}
              
              {/* 已观看标识 */}
              {episode.isWatched && currentEpisode !== episode.episodeNumber && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <Check className="w-2.5 h-2.5 text-white" />
                </div>
              )}
              
              {/* 观看进度条 */}
              {episode.watchProgress && episode.watchProgress > 0 && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20 rounded-b-lg overflow-hidden">
                  <div
                    className="h-full bg-primary-400 transition-all duration-300"
                    style={{ width: `${episode.watchProgress * 100}%` }}
                  />
                </div>
              )}
            </motion.button>
          ))}
        </div>
        
        {/* 集数统计 */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <div>
            已观看: {episodes.filter(ep => ep.isWatched).length} 集
          </div>
          <div>
            总计: {episodes.length} 集
          </div>
        </div>
      </div>
    </div>
  );
}
