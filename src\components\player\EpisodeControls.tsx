import React from 'react';
import { motion } from 'framer-motion';
import { Skip<PERSON><PERSON>, Ski<PERSON><PERSON>or<PERSON>, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils';

interface EpisodeControlsProps {
  currentEpisode: number;
  totalEpisodes: number;
  onPreviousEpisode: () => void;
  onNextEpisode: () => void;
  onReplay?: () => void;
  className?: string;
}

/**
 * 剧集控制组件
 */
export function EpisodeControls({
  currentEpisode,
  totalEpisodes,
  onPreviousEpisode,
  onNextEpisode,
  onReplay,
  className,
}: EpisodeControlsProps) {
  const hasPrevious = currentEpisode > 1;
  const hasNext = currentEpisode < totalEpisodes;

  return (
    <div className={cn('flex items-center justify-center space-x-4', className)}>
      {/* 上一集按钮 */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Button
          variant="secondary"
          size="lg"
          onClick={onPreviousEpisode}
          disabled={!hasPrevious}
          className={cn(
            'flex items-center space-x-2 min-w-[120px]',
            !hasPrevious && 'opacity-50 cursor-not-allowed'
          )}
        >
          <SkipBack className="w-5 h-5" />
          <span>上一集</span>
        </Button>
      </motion.div>

      {/* 重播按钮 */}
      {onReplay && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Button
            variant="outline"
            size="lg"
            onClick={onReplay}
            className="flex items-center space-x-2"
          >
            <RotateCcw className="w-5 h-5" />
            <span>重播</span>
          </Button>
        </motion.div>
      )}

      {/* 下一集按钮 */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Button
          variant="primary"
          size="lg"
          onClick={onNextEpisode}
          disabled={!hasNext}
          className={cn(
            'flex items-center space-x-2 min-w-[120px]',
            !hasNext && 'opacity-50 cursor-not-allowed'
          )}
        >
          <span>下一集</span>
          <SkipForward className="w-5 h-5" />
        </Button>
      </motion.div>
    </div>
  );
}

/**
 * 自动播放下一集提示组件
 */
interface AutoPlayNotificationProps {
  show: boolean;
  countdown: number;
  onCancel: () => void;
  onPlayNow: () => void;
}

export function AutoPlayNotification({
  show,
  countdown,
  onCancel,
  onPlayNow,
}: AutoPlayNotificationProps) {
  if (!show) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
      className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50"
    >
      <div className="bg-black/90 text-white rounded-lg p-4 flex items-center space-x-4 shadow-xl">
        <div className="flex-1">
          <div className="text-sm font-medium">即将播放下一集</div>
          <div className="text-xs text-gray-300">
            {countdown} 秒后自动播放
          </div>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={onCancel}
            className="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-500 rounded transition-colors"
          >
            取消
          </button>
          <button
            onClick={onPlayNow}
            className="px-3 py-1 text-sm bg-primary-600 hover:bg-primary-500 rounded transition-colors"
          >
            立即播放
          </button>
        </div>
      </div>
    </motion.div>
  );
}
