import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings, Check, Zap } from 'lucide-react';
import { cn } from '@/utils';
import type { VideoQuality } from '@/types';

interface QualitySelectorProps {
  qualities: VideoQuality[];
  currentQuality: string;
  onQualityChange: (quality: string) => void;
  className?: string;
}

/**
 * 画质选择组件
 */
export function QualitySelector({
  qualities,
  currentQuality,
  onQualityChange,
  className,
}: QualitySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const currentQualityInfo = qualities.find(q => q.value === currentQuality);

  return (
    <div className={cn('relative', className)} ref={menuRef}>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors',
          'bg-black/20 hover:bg-black/30 text-white text-sm',
          isOpen && 'bg-black/40'
        )}
      >
        <Settings className="w-4 h-4" />
        <span>{currentQualityInfo?.label || '自动'}</span>
      </button>

      {/* 下拉菜单 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.15 }}
            className="absolute bottom-full right-0 mb-2 min-w-[160px] bg-black/90 backdrop-blur-sm rounded-lg shadow-xl border border-white/10 overflow-hidden"
          >
            <div className="py-2">
              {/* 画质选项 */}
              <div className="px-3 py-2 text-xs text-gray-400 font-medium border-b border-white/10">
                画质选择
              </div>
              
              {qualities.map((quality, index) => (
                <motion.button
                  key={quality.value}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.1, delay: index * 0.05 }}
                  onClick={() => {
                    onQualityChange(quality.value);
                    setIsOpen(false);
                  }}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-2 text-sm transition-colors',
                    'hover:bg-white/10',
                    currentQuality === quality.value
                      ? 'text-primary-400 bg-primary-500/20'
                      : 'text-white'
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <span>{quality.label}</span>
                    {quality.value === 'auto' && (
                      <Zap className="w-3 h-3 text-yellow-400" />
                    )}
                  </div>
                  
                  {currentQuality === quality.value && (
                    <Check className="w-4 h-4 text-primary-400" />
                  )}
                </motion.button>
              ))}
              
              {/* 倍速选项 */}
              <div className="px-3 py-2 text-xs text-gray-400 font-medium border-t border-white/10 mt-2">
                播放速度
              </div>
              
              {[0.5, 0.75, 1, 1.25, 1.5, 2].map((speed, index) => (
                <motion.button
                  key={speed}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.1, delay: (qualities.length + index) * 0.05 }}
                  onClick={() => {
                    // 这里可以添加播放速度变化的回调
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-white hover:bg-white/10 transition-colors"
                >
                  <span>{speed}x</span>
                  {speed === 1 && <span className="text-xs text-gray-400">正常</span>}
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

/**
 * 简化的画质指示器组件
 */
interface QualityIndicatorProps {
  quality: string;
  className?: string;
}

export function QualityIndicator({ quality, className }: QualityIndicatorProps) {
  const getQualityColor = (q: string) => {
    switch (q) {
      case '1080p':
      case 'original':
        return 'text-green-400';
      case '720p':
        return 'text-blue-400';
      case '480p':
        return 'text-yellow-400';
      case 'auto':
        return 'text-purple-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      <div className={cn('w-2 h-2 rounded-full', getQualityColor(quality))} />
      <span className={cn('text-xs font-medium', getQualityColor(quality))}>
        {quality.toUpperCase()}
      </span>
    </div>
  );
}
