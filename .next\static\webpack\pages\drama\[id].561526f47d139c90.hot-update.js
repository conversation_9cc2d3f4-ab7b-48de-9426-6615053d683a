"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/drama/[id]",{

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件 - 简化版本\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"720p\");\n    const [totalEpisodes, setTotalEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(80);\n    /**\n   * 画质选项\n   */ const qualityOptions = [\n        {\n            label: \"高清 720p\",\n            value: \"720p\"\n        },\n        {\n            label: \"超清 1080p\",\n            value: \"1080p\"\n        },\n        {\n            label: \"原始画质\",\n            value: \"original\"\n        }\n    ];\n    /**\n   * 获取短剧详情 - 简化版本\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // 使用模拟数据，避免API错误\n            const mockDrama = {\n                book_id: dramaId,\n                title: \"甜妻似火，霍爷他超爱\",\n                desc: \"七年前，苏卿卿误入霍氏总裁霍玄夜房间，一夜纠缠后诞下双胞胎。大宝被苏宏瑞指使人偷走遗弃，只剩小宝苏小果相伴。七年后，苏卿卿携子回国寻子，与霍玄夜重逢却不相识，两人在多次接触中情愫暗生。\",\n                cover: \"/api/placeholder/400/600\",\n                author: \"霍玄夜,苏卿卿\",\n                category_schema: \"萌宝\\xb7现代言情\\xb780集\",\n                update_time: \"2024-01-15\"\n            };\n            setDrama(mockDrama);\n            setTotalEpisodes(80);\n        } catch (err) {\n            setError(\"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = (episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n    };\n    /**\n   * 处理画质选择\n   */ const handleQualityChange = (quality)=>{\n        setCurrentQuality(quality);\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    const watchHistory = getDramaHistory(drama.book_id);\n    const isWatched = hasWatched(drama.book_id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: `${drama.title},${drama.author},${drama.category_schema},短剧`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: drama.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: drama.cover\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"video.movie\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_7__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"bg-white rounded-xl shadow-soft overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-video bg-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlayer, {\n                                                        drama: drama,\n                                                        videoUrl: episodes[currentEpisode - 1]?.videoUrl,\n                                                        onEnded: ()=>{},\n                                                        onError: (error)=>console.error(\"播放错误:\", error),\n                                                        className: \"w-full h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: drama.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-300\",\n                                                                    children: [\n                                                                        \"第\",\n                                                                        currentEpisode,\n                                                                        \"集 / 共\",\n                                                                        drama.totalEpisodes,\n                                                                        \"集\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualityIndicator, {\n                                                            quality: currentQuality\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeControls, {\n                                                            currentEpisode: currentEpisode,\n                                                            totalEpisodes: drama.totalEpisodes || 1,\n                                                            onPreviousEpisode: ()=>{\n                                                                if (currentEpisode > 1) {\n                                                                    handleEpisodeSelect(currentEpisode - 1);\n                                                                }\n                                                            },\n                                                            onNextEpisode: ()=>{\n                                                                if (drama && currentEpisode < drama.totalEpisodes) {\n                                                                    handleEpisodeSelect(currentEpisode + 1);\n                                                                }\n                                                            },\n                                                            onReplay: ()=>{\n                                                                if (drama) {\n                                                                    addToHistory({\n                                                                        ...drama,\n                                                                        currentEpisode\n                                                                    });\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualitySelector, {\n                                                            qualities: videoQualities,\n                                                            currentQuality: currentQuality,\n                                                            onQualityChange: (quality)=>{\n                                                                setCurrentQuality(quality);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeList, {\n                                            episodes: episodes,\n                                            currentEpisode: currentEpisode,\n                                            onEpisodeSelect: handleEpisodeSelect\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"78OOynEE0rGuNV7adbQgvA+UiqA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

});