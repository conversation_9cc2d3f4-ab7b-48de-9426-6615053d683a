{"c": ["pages/drama/[id]", "webpack"], "r": ["pages/index"], "m": ["./node_modules/lucide-react/dist/esm/icons/eye.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCursor%20Project%5C%E7%9F%AD%E5%89%A7%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./node_modules/next/dist/client/image-component.js", "./node_modules/next/dist/compiled/picomatch/index.js", "./node_modules/next/dist/shared/lib/get-img-props.js", "./node_modules/next/dist/shared/lib/image-blur-svg.js", "./node_modules/next/dist/shared/lib/image-external.js", "./node_modules/next/dist/shared/lib/image-loader.js", "./node_modules/next/dist/shared/lib/match-local-pattern.js", "./node_modules/next/dist/shared/lib/match-remote-pattern.js", "./node_modules/next/image.js", "./src/components/drama/DramaCard.tsx", "./src/pages/index.tsx", "__barrel_optimize__?names=<PERSON>,<PERSON>,Play,Star!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Clock,Search,Star,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}