"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/drama/[id]",{

/***/ "__barrel_optimize__?names=ArrowLeft,Maximize,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowLeft,Maximize,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowLeft: function() { return /* reexport safe */ _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Maximize: function() { return /* reexport safe */ _icons_maximize_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   SkipBack: function() { return /* reexport safe */ _icons_skip_back_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   SkipForward: function() { return /* reexport safe */ _icons_skip_forward_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-left.js */ \"./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _icons_maximize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/maximize.js */ \"./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _icons_skip_back_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/skip-back.js */ \"./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var _icons_skip_forward_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/skip-forward.js */ \"./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0xlZnQsTWF4aW1pemUsU2tpcEJhY2ssU2tpcEZvcndhcmQhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUM0RDtBQUNIO0FBQ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/NjZhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dMZWZ0IH0gZnJvbSBcIi4vaWNvbnMvYXJyb3ctbGVmdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1heGltaXplIH0gZnJvbSBcIi4vaWNvbnMvbWF4aW1pemUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTa2lwQmFjayB9IGZyb20gXCIuL2ljb25zL3NraXAtYmFjay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNraXBGb3J3YXJkIH0gZnJvbSBcIi4vaWNvbnMvc2tpcC1mb3J3YXJkLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowLeft,Maximize,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/maximize.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/maximize.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Maximize; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst Maximize = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Maximize\", [\n    [\n        \"path\",\n        {\n            d: \"M8 3H5a2 2 0 0 0-2 2v3\",\n            key: \"1dcmit\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 8V5a2 2 0 0 0-2-2h-3\",\n            key: \"1e4gt3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 16v3a2 2 0 0 0 2 2h3\",\n            key: \"wsl5sc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 21h3a2 2 0 0 0 2-2v-3\",\n            key: \"18trek\"\n        }\n    ]\n]);\n //# sourceMappingURL=maximize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/maximize.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/skip-back.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/skip-back.js ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SkipBack; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst SkipBack = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"SkipBack\", [\n    [\n        \"polygon\",\n        {\n            points: \"19 20 9 12 19 4 19 20\",\n            key: \"o2sva\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"5\",\n            x2: \"5\",\n            y1: \"19\",\n            y2: \"5\",\n            key: \"1ocqjk\"\n        }\n    ]\n]);\n //# sourceMappingURL=skip-back.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/skip-back.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/skip-forward.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/skip-forward.js ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SkipForward; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.290.0 - ISC\n */ \nconst SkipForward = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"SkipForward\", [\n    [\n        \"polygon\",\n        {\n            points: \"5 4 15 12 5 20 5 4\",\n            key: \"16p6eg\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"19\",\n            x2: \"19\",\n            y1: \"5\",\n            y2: \"19\",\n            key: \"futhcm\"\n        }\n    ]\n]);\n //# sourceMappingURL=skip-forward.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/skip-forward.js\n"));

/***/ }),

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize,SkipBack,SkipForward!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft,Maximize,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件 - 简化版本\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"720p\");\n    const [totalEpisodes, setTotalEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(80);\n    /**\n   * 画质选项\n   */ const qualityOptions = [\n        {\n            label: \"高清 720p\",\n            value: \"720p\"\n        },\n        {\n            label: \"超清 1080p\",\n            value: \"1080p\"\n        },\n        {\n            label: \"原始画质\",\n            value: \"original\"\n        }\n    ];\n    /**\n   * 获取短剧详情 - 简化版本\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // 使用模拟数据，避免API错误\n            const mockDrama = {\n                book_id: dramaId,\n                title: \"甜妻似火，霍爷他超爱\",\n                desc: \"七年前，苏卿卿误入霍氏总裁霍玄夜房间，一夜纠缠后诞下双胞胎。大宝被苏宏瑞指使人偷走遗弃，只剩小宝苏小果相伴。七年后，苏卿卿携子回国寻子，与霍玄夜重逢却不相识，两人在多次接触中情愫暗生。\",\n                cover: \"/api/placeholder/400/600\",\n                author: \"霍玄夜,苏卿卿\",\n                category_schema: \"萌宝\\xb7现代言情\\xb780集\",\n                update_time: \"2024-01-15\"\n            };\n            setDrama(mockDrama);\n            setTotalEpisodes(80);\n        } catch (err) {\n            setError(\"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = (episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n    };\n    /**\n   * 处理画质选择\n   */ const handleQualityChange = (quality)=>{\n        setCurrentQuality(quality);\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_7__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm p-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>currentEpisode > 1 && handleEpisodeSelect(currentEpisode - 1),\n                                                            disabled: currentEpisode <= 1,\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_7__.SkipBack, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"上一集\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>currentEpisode < totalEpisodes && handleEpisodeSelect(currentEpisode + 1),\n                                                            disabled: currentEpisode >= totalEpisodes,\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                \"下一集\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_7__.SkipForward, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"primary\",\n                                                            size: \"sm\",\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize_SkipBack_SkipForward_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Maximize, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"全屏播放\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"画质:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: currentQuality,\n                                                            onChange: (e)=>handleQualityChange(e.target.value),\n                                                            className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: qualityOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                    children: drama.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"画质:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" \",\n                                                                qualityOptions.find((q)=>q.value === currentQuality)?.label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"时长:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" 约8分钟\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"大小:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" 约50MB\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"当前:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" 第\",\n                                                                currentEpisode,\n                                                                \"集\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: [\n                                                \"剧集列表 (共\",\n                                                totalEpisodes,\n                                                \"集)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 gap-2\",\n                                            children: Array.from({\n                                                length: totalEpisodes\n                                            }, (_, index)=>{\n                                                const episodeNumber = index + 1;\n                                                const isActive = episodeNumber === currentEpisode;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEpisodeSelect(episodeNumber),\n                                                    className: `\n                        px-3 py-2 text-sm rounded transition-colors\n                        ${isActive ? \"bg-blue-500 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"}\n                      `,\n                                                    children: episodeNumber\n                                                }, episodeNumber, false, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"78OOynEE0rGuNV7adbQgvA+UiqA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

});