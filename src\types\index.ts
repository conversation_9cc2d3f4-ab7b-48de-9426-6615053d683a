/**
 * 短剧集数信息类型
 */
export interface Episode {
  id: string;
  episodeNumber: number;
  title: string;
  duration: number; // 时长（秒）
  videoUrl?: string;
  thumbnailUrl?: string;
  isWatched?: boolean;
  watchProgress?: number; // 观看进度 (0-1)
}

/**
 * 视频质量选项类型
 */
export interface VideoQuality {
  label: string;
  value: string;
  url?: string;
  bitrate?: number;
}

/**
 * 短剧数据类型定义
 */
export interface Drama {
  book_id: string;
  title: string;
  author: string;
  type: string;
  cover: string;
  category_schema: string;
  desc: string;
  // 新增字段
  totalEpisodes?: number;
  episodes?: Episode[];
  rating?: number;
  releaseDate?: string;
  tags?: string[];
  videoQualities?: VideoQuality[];
  currentEpisode?: number;
}

/**
 * API响应类型
 */
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 搜索响应类型
 */
export interface SearchResponse {
  code: number;
  msg: string;
  data: Drama[];
}

/**
 * 播放历史记录类型
 */
export interface WatchHistory {
  id: string;
  drama: Drama;
  watchedAt: number;
  progress: number; // 播放进度 (0-1)
  episode?: number; // 当前集数
  totalEpisodes?: number; // 总集数
}

/**
 * 搜索历史类型
 */
export interface SearchHistory {
  id: string;
  keyword: string;
  searchedAt: number;
}

/**
 * 用户偏好设置类型
 */
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  autoPlay: boolean;
  defaultQuality: 'auto' | '720p' | '1080p' | 'original';
  playbackSpeed: number;
  volume: number;
}

/**
 * 视频播放器状态类型
 */
export interface PlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  quality: string;
  playbackRate: number;
  isFullscreen: boolean;
  isLoading: boolean;
  error?: string;
}

/**
 * 应用状态类型
 */
export interface AppState {
  isLoading: boolean;
  error?: string;
  networkStatus: 'online' | 'offline';
  installPrompt?: BeforeInstallPromptEvent;
}

/**
 * PWA安装提示事件类型
 */
export interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

/**
 * 组件通用Props类型
 */
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

/**
 * 分页参数类型
 */
export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * 搜索参数类型
 */
export interface SearchParams extends PaginationParams {
  keyword: string;
  category?: string;
  sortBy?: 'relevance' | 'date' | 'popularity';
}

/**
 * 错误类型
 */
export interface AppError {
  code: string;
  message: string;
  details?: unknown;
}

/**
 * 网络请求配置类型
 */
export interface RequestConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
}

/**
 * 缓存配置类型
 */
export interface CacheConfig {
  key: string;
  ttl: number; // 缓存时间（毫秒）
  maxSize?: number; // 最大缓存条目数
}

/**
 * 性能监控数据类型
 */
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  apiResponseTime: number;
  memoryUsage?: number;
}

/**
 * 设备信息类型
 */
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  userAgent: string;
  screenSize: {
    width: number;
    height: number;
  };
  connection?: {
    effectiveType: string;
    downlink: number;
  };
}
