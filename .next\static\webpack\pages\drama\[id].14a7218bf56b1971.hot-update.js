"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/drama/[id]",{

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"./src/lib/api.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件 - 简化版本\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"720p\");\n    const [totalEpisodes, setTotalEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(80);\n    /**\n   * 生成模拟集数数据\n   */ const generateMockEpisodes = (totalEpisodes)=>{\n        return Array.from({\n            length: totalEpisodes\n        }, (_, index)=>({\n                id: `episode-${index + 1}`,\n                episodeNumber: index + 1,\n                title: `第${index + 1}集`,\n                duration: 300 + Math.random() * 600,\n                videoUrl: `https://example.com/videos/drama-${id}/episode-${index + 1}.mp4`,\n                thumbnailUrl: `https://example.com/thumbnails/drama-${id}/episode-${index + 1}.jpg`,\n                isWatched: Math.random() > 0.7,\n                watchProgress: Math.random() > 0.5 ? Math.random() : 0\n            }));\n    };\n    /**\n   * 生成模拟画质选项\n   */ const generateMockQualities = ()=>{\n        return [\n            {\n                label: \"自动\",\n                value: \"auto\"\n            },\n            {\n                label: \"1080P\",\n                value: \"1080p\",\n                bitrate: 2000\n            },\n            {\n                label: \"720P\",\n                value: \"720p\",\n                bitrate: 1200\n            },\n            {\n                label: \"480P\",\n                value: \"480p\",\n                bitrate: 800\n            }\n        ];\n    };\n    /**\n   * 获取短剧详情\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const dramaData = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getDramaDetail(dramaId);\n            // 扩展短剧数据\n            const totalEpisodes = Math.floor(Math.random() * 50) + 10; // 10-60集\n            const extendedDrama = {\n                ...dramaData,\n                totalEpisodes,\n                rating: 4.2 + Math.random() * 0.6,\n                releaseDate: \"2024-01-15\",\n                tags: dramaData.category_schema.split(\"、\"),\n                currentEpisode: 1\n            };\n            setDrama(extendedDrama);\n            setEpisodes(generateMockEpisodes(totalEpisodes));\n            setVideoQualities(generateMockQualities());\n            // 获取相关推荐\n            const related = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getRecommendedDramas(8);\n            setRelatedDramas(related.filter((item)=>item.book_id !== dramaId));\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = useCallback((episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode: episodeNumber\n            });\n        }\n    }, [\n        drama,\n        addToHistory\n    ]);\n    /**\n   * 处理播放\n   */ const handlePlay = ()=>{\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode\n            });\n            setShowPlayer(true);\n        }\n    };\n    /**\n   * 处理收藏\n   */ const handleFavorite = ()=>{\n        setIsFavorited(!isFavorited);\n    };\n    /**\n   * 处理分享\n   */ const handleShare = async ()=>{\n        if (!drama) return;\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: drama.title,\n                    text: drama.desc,\n                    url: window.location.href\n                });\n            } catch (err) {\n                console.log(\"分享取消\");\n            }\n        } else {\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    const watchHistory = getDramaHistory(drama.book_id);\n    const isWatched = hasWatched(drama.book_id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: `${drama.title},${drama.author},${drama.category_schema},短剧`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: drama.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: drama.cover\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"video.movie\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"bg-white rounded-xl shadow-soft overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-video bg-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlayer, {\n                                                        drama: drama,\n                                                        videoUrl: episodes[currentEpisode - 1]?.videoUrl,\n                                                        onEnded: ()=>{},\n                                                        onError: (error)=>console.error(\"播放错误:\", error),\n                                                        className: \"w-full h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: drama.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-300\",\n                                                                    children: [\n                                                                        \"第\",\n                                                                        currentEpisode,\n                                                                        \"集 / 共\",\n                                                                        drama.totalEpisodes,\n                                                                        \"集\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualityIndicator, {\n                                                            quality: currentQuality\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeControls, {\n                                                            currentEpisode: currentEpisode,\n                                                            totalEpisodes: drama.totalEpisodes || 1,\n                                                            onPreviousEpisode: ()=>{\n                                                                if (currentEpisode > 1) {\n                                                                    handleEpisodeSelect(currentEpisode - 1);\n                                                                }\n                                                            },\n                                                            onNextEpisode: ()=>{\n                                                                if (drama && currentEpisode < drama.totalEpisodes) {\n                                                                    handleEpisodeSelect(currentEpisode + 1);\n                                                                }\n                                                            },\n                                                            onReplay: ()=>{\n                                                                if (drama) {\n                                                                    addToHistory({\n                                                                        ...drama,\n                                                                        currentEpisode\n                                                                    });\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualitySelector, {\n                                                            qualities: videoQualities,\n                                                            currentQuality: currentQuality,\n                                                            onQualityChange: (quality)=>{\n                                                                setCurrentQuality(quality);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeList, {\n                                            episodes: episodes,\n                                            currentEpisode: currentEpisode,\n                                            onEpisodeSelect: handleEpisodeSelect\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"sb5x2aVRQvpsalUXhhTAsJ9MuWE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

});