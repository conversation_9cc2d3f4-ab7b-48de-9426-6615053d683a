"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/drama/[id]",{

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件 - 简化版本\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"720p\");\n    const [totalEpisodes, setTotalEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(80);\n    /**\n   * 画质选项\n   */ const qualityOptions = [\n        {\n            label: \"高清 720p\",\n            value: \"720p\"\n        },\n        {\n            label: \"超清 1080p\",\n            value: \"1080p\"\n        },\n        {\n            label: \"原始画质\",\n            value: \"original\"\n        }\n    ];\n    /**\n   * 获取短剧详情 - 简化版本\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // 使用模拟数据，避免API错误\n            const mockDrama = {\n                book_id: dramaId,\n                title: \"甜妻似火，霍爷他超爱\",\n                desc: \"七年前，苏卿卿误入霍氏总裁霍玄夜房间，一夜纠缠后诞下双胞胎。大宝被苏宏瑞指使人偷走遗弃，只剩小宝苏小果相伴。七年后，苏卿卿携子回国寻子，与霍玄夜重逢却不相识，两人在多次接触中情愫暗生。\",\n                cover: \"/api/placeholder/400/600\",\n                author: \"霍玄夜,苏卿卿\",\n                category_schema: \"萌宝\\xb7现代言情\\xb780集\",\n                update_time: \"2024-01-15\"\n            };\n            setDrama(mockDrama);\n            setTotalEpisodes(80);\n        } catch (err) {\n            setError(\"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = (episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n    };\n    /**\n   * 处理画质选择\n   */ const handleQualityChange = (quality)=>{\n        setCurrentQuality(quality);\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: `${drama.title},${drama.author},${drama.category_schema},短剧`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: drama.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: drama.cover\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"video.movie\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_7__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"bg-white rounded-xl shadow-soft overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-video bg-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlayer, {\n                                                        drama: drama,\n                                                        videoUrl: episodes[currentEpisode - 1]?.videoUrl,\n                                                        onEnded: ()=>{},\n                                                        onError: (error)=>console.error(\"播放错误:\", error),\n                                                        className: \"w-full h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: drama.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-300\",\n                                                                    children: [\n                                                                        \"第\",\n                                                                        currentEpisode,\n                                                                        \"集 / 共\",\n                                                                        drama.totalEpisodes,\n                                                                        \"集\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualityIndicator, {\n                                                            quality: currentQuality\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeControls, {\n                                                            currentEpisode: currentEpisode,\n                                                            totalEpisodes: drama.totalEpisodes || 1,\n                                                            onPreviousEpisode: ()=>{\n                                                                if (currentEpisode > 1) {\n                                                                    handleEpisodeSelect(currentEpisode - 1);\n                                                                }\n                                                            },\n                                                            onNextEpisode: ()=>{\n                                                                if (drama && currentEpisode < drama.totalEpisodes) {\n                                                                    handleEpisodeSelect(currentEpisode + 1);\n                                                                }\n                                                            },\n                                                            onReplay: ()=>{\n                                                                if (drama) {\n                                                                    addToHistory({\n                                                                        ...drama,\n                                                                        currentEpisode\n                                                                    });\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualitySelector, {\n                                                            qualities: videoQualities,\n                                                            currentQuality: currentQuality,\n                                                            onQualityChange: (quality)=>{\n                                                                setCurrentQuality(quality);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeList, {\n                                            episodes: episodes,\n                                            currentEpisode: currentEpisode,\n                                            onEpisodeSelect: handleEpisodeSelect\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"78OOynEE0rGuNV7adbQgvA+UiqA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

});