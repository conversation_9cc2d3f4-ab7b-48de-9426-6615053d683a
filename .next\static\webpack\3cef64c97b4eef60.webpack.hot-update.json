{"c": ["pages/drama/[id]", "webpack"], "r": [], "m": ["./node_modules/lucide-react/dist/esm/icons/check.js", "./node_modules/lucide-react/dist/esm/icons/loader.js", "./node_modules/lucide-react/dist/esm/icons/maximize.js", "./node_modules/lucide-react/dist/esm/icons/minimize.js", "./node_modules/lucide-react/dist/esm/icons/pause.js", "./node_modules/lucide-react/dist/esm/icons/play.js", "./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js", "./node_modules/lucide-react/dist/esm/icons/skip-back.js", "./node_modules/lucide-react/dist/esm/icons/skip-forward.js", "./node_modules/lucide-react/dist/esm/icons/volume-1.js", "./node_modules/lucide-react/dist/esm/icons/volume-2.js", "./node_modules/lucide-react/dist/esm/icons/volume-x.js", "./node_modules/lucide-react/dist/esm/icons/zap.js", "./src/components/drama/EpisodeList.tsx", "./src/components/player/EpisodeControls.tsx", "./src/components/player/PlayerControls.tsx", "./src/components/player/ProgressBar.tsx", "./src/components/player/QualitySelector.tsx", "./src/components/player/SettingsMenu.tsx", "./src/components/player/VideoPlayer.tsx", "./src/components/player/VolumeControl.tsx", "./src/hooks/usePlayer.ts", "./src/hooks/useWatchHistory.ts", "__barrel_optimize__?names=Check!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Check,Play!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Check,<PERSON>ting<PERSON>,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Loader,Maximize,Minimize,Play,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Pause,Play,Skip<PERSON><PERSON>,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=<PERSON>otateCcw,SkipBack,SkipForward!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Volume1,Volume2,VolumeX!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}