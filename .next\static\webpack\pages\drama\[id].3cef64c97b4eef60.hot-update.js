"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/drama/[id]",{

/***/ "./src/pages/drama/[id].tsx":
/*!**********************************!*\
  !*** ./src/pages/drama/[id].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DramaDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Loading */ \"./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"./src/lib/api.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * 短剧详情页面组件\n */ function DramaDetailPage() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { id } = router.query;\n    const [drama, setDrama] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [relatedDramas, setRelatedDramas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPlayer, setShowPlayer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorited, setIsFavorited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 新增状态\n    const [currentEpisode, setCurrentEpisode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentQuality, setCurrentQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"auto\");\n    const [showAutoPlayNotification, setShowAutoPlayNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoPlayCountdown, setAutoPlayCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [episodes, setEpisodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [videoQualities, setVideoQualities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { addToHistory, getDramaHistory, hasWatched } = useWatchHistory();\n    /**\n   * 生成模拟集数数据\n   */ const generateMockEpisodes = (totalEpisodes)=>{\n        return Array.from({\n            length: totalEpisodes\n        }, (_, index)=>({\n                id: `episode-${index + 1}`,\n                episodeNumber: index + 1,\n                title: `第${index + 1}集`,\n                duration: 300 + Math.random() * 600,\n                videoUrl: `https://example.com/videos/drama-${id}/episode-${index + 1}.mp4`,\n                thumbnailUrl: `https://example.com/thumbnails/drama-${id}/episode-${index + 1}.jpg`,\n                isWatched: Math.random() > 0.7,\n                watchProgress: Math.random() > 0.5 ? Math.random() : 0\n            }));\n    };\n    /**\n   * 生成模拟画质选项\n   */ const generateMockQualities = ()=>{\n        return [\n            {\n                label: \"自动\",\n                value: \"auto\"\n            },\n            {\n                label: \"1080P\",\n                value: \"1080p\",\n                bitrate: 2000\n            },\n            {\n                label: \"720P\",\n                value: \"720p\",\n                bitrate: 1200\n            },\n            {\n                label: \"480P\",\n                value: \"480p\",\n                bitrate: 800\n            }\n        ];\n    };\n    /**\n   * 获取短剧详情\n   */ const fetchDramaDetail = async (dramaId)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const dramaData = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getDramaDetail(dramaId);\n            // 扩展短剧数据\n            const totalEpisodes = Math.floor(Math.random() * 50) + 10; // 10-60集\n            const extendedDrama = {\n                ...dramaData,\n                totalEpisodes,\n                rating: 4.2 + Math.random() * 0.6,\n                releaseDate: \"2024-01-15\",\n                tags: dramaData.category_schema.split(\"、\"),\n                currentEpisode: 1\n            };\n            setDrama(extendedDrama);\n            setEpisodes(generateMockEpisodes(totalEpisodes));\n            setVideoQualities(generateMockQualities());\n            // 获取相关推荐\n            const related = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getRecommendedDramas(8);\n            setRelatedDramas(related.filter((item)=>item.book_id !== dramaId));\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取短剧详情失败\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * 处理集数选择\n   */ const handleEpisodeSelect = useCallback((episodeNumber)=>{\n        setCurrentEpisode(episodeNumber);\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode: episodeNumber\n            });\n        }\n    }, [\n        drama,\n        addToHistory\n    ]);\n    /**\n   * 处理播放\n   */ const handlePlay = ()=>{\n        if (drama) {\n            addToHistory({\n                ...drama,\n                currentEpisode\n            });\n            setShowPlayer(true);\n        }\n    };\n    /**\n   * 处理收藏\n   */ const handleFavorite = ()=>{\n        setIsFavorited(!isFavorited);\n    };\n    /**\n   * 处理分享\n   */ const handleShare = async ()=>{\n        if (!drama) return;\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: drama.title,\n                    text: drama.desc,\n                    url: window.location.href\n                });\n            } catch (err) {\n                console.log(\"分享取消\");\n            }\n        } else {\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    /**\n   * 返回上一页\n   */ const handleGoBack = ()=>{\n        router.back();\n    };\n    /**\n   * 初始化数据\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (id && typeof id === \"string\") {\n            fetchDramaDetail(id);\n        }\n    }, [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_5__.Loading, {\n                    size: \"lg\",\n                    text: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !drama) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE15\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: error || \"短剧不存在\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"抱歉，无法找到您要查看的短剧\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleGoBack,\n                            children: \"返回上一页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    }\n    const watchHistory = getDramaHistory(drama.book_id);\n    const isWatched = hasWatched(drama.book_id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            drama.title,\n                            \" - 短剧搜索\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: `${drama.title},${drama.author},${drama.category_schema},短剧`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: drama.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: drama.desc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: drama.cover\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"video.movie\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_6__.SimpleLayout, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ArrowLeft, {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"返回\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"bg-white rounded-xl shadow-soft overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-video bg-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlayer, {\n                                                        drama: drama,\n                                                        videoUrl: episodes[currentEpisode - 1]?.videoUrl,\n                                                        onEnded: ()=>{},\n                                                        onError: (error)=>console.error(\"播放错误:\", error),\n                                                        className: \"w-full h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: drama.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-300\",\n                                                                    children: [\n                                                                        \"第\",\n                                                                        currentEpisode,\n                                                                        \"集 / 共\",\n                                                                        drama.totalEpisodes,\n                                                                        \"集\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualityIndicator, {\n                                                            quality: currentQuality\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeControls, {\n                                                            currentEpisode: currentEpisode,\n                                                            totalEpisodes: drama.totalEpisodes || 1,\n                                                            onPreviousEpisode: ()=>{\n                                                                if (currentEpisode > 1) {\n                                                                    handleEpisodeSelect(currentEpisode - 1);\n                                                                }\n                                                            },\n                                                            onNextEpisode: ()=>{\n                                                                if (drama && currentEpisode < drama.totalEpisodes) {\n                                                                    handleEpisodeSelect(currentEpisode + 1);\n                                                                }\n                                                            },\n                                                            onReplay: ()=>{\n                                                                if (drama) {\n                                                                    addToHistory({\n                                                                        ...drama,\n                                                                        currentEpisode\n                                                                    });\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualitySelector, {\n                                                            qualities: videoQualities,\n                                                            currentQuality: currentQuality,\n                                                            onQualityChange: (quality)=>{\n                                                                setCurrentQuality(quality);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EpisodeList, {\n                                            episodes: episodes,\n                                            currentEpisode: currentEpisode,\n                                            onEpisodeSelect: handleEpisodeSelect\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\短剧\\\\src\\\\pages\\\\drama\\\\[id].tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DramaDetailPage, \"gV0cVYym46QJYZNmXvhLREmNYU8=\", true, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DramaDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DramaDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/drama/[id].tsx\n"));

/***/ })

});