import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Heart, 
  Share2, 
  Download, 
  Star, 
  Clock, 
  Calendar,
  User,
  Tag,
  Eye,
  ArrowLeft,
  Settings,
  Maximize
} from 'lucide-react';
import { VideoPlayer } from '@/components/player/VideoPlayer';
import { EpisodeList } from '@/components/drama/EpisodeList';
import { EpisodeControls, AutoPlayNotification } from '@/components/player/EpisodeControls';
import { QualitySelector, QualityIndicator } from '@/components/player/QualitySelector';
import { DramaCard } from '@/components/drama/DramaCard';
import { Button, IconButton } from '@/components/ui/Button';
import { Loading } from '@/components/ui/Loading';
import { SimpleLayout } from '@/components/layout/Layout';
import { useWatchHistory } from '@/hooks/useWatchHistory';
import { api } from '@/lib/api';
import { formatTime, cn } from '@/utils';
import type { Drama, Episode, VideoQuality } from '@/types';

/**
 * 短剧详情页面组件
 */
export default function DramaDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const [drama, setDrama] = useState<Drama | null>(null);
  const [relatedDramas, setRelatedDramas] = useState<Drama[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPlayer, setShowPlayer] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  
  // 新增状态
  const [currentEpisode, setCurrentEpisode] = useState(1);
  const [currentQuality, setCurrentQuality] = useState('auto');
  const [showAutoPlayNotification, setShowAutoPlayNotification] = useState(false);
  const [autoPlayCountdown, setAutoPlayCountdown] = useState(5);
  const [episodes, setEpisodes] = useState<Episode[]>([]);
  const [videoQualities, setVideoQualities] = useState<VideoQuality[]>([]);

  const { addToHistory, getDramaHistory, hasWatched } = useWatchHistory();

  /**
   * 生成模拟集数数据
   */
  const generateMockEpisodes = (totalEpisodes: number): Episode[] => {
    return Array.from({ length: totalEpisodes }, (_, index) => ({
      id: `episode-${index + 1}`,
      episodeNumber: index + 1,
      title: `第${index + 1}集`,
      duration: 300 + Math.random() * 600, // 5-15分钟
      videoUrl: `https://example.com/videos/drama-${id}/episode-${index + 1}.mp4`,
      thumbnailUrl: `https://example.com/thumbnails/drama-${id}/episode-${index + 1}.jpg`,
      isWatched: Math.random() > 0.7, // 30%的概率已观看
      watchProgress: Math.random() > 0.5 ? Math.random() : 0, // 50%的概率有观看进度
    }));
  };

  /**
   * 生成模拟画质选项
   */
  const generateMockQualities = (): VideoQuality[] => {
    return [
      { label: '自动', value: 'auto' },
      { label: '1080P', value: '1080p', bitrate: 2000 },
      { label: '720P', value: '720p', bitrate: 1200 },
      { label: '480P', value: '480p', bitrate: 800 },
    ];
  };

  /**
   * 获取短剧详情
   */
  const fetchDramaDetail = async (dramaId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const dramaData = await api.getDramaDetail(dramaId);
      
      // 扩展短剧数据
      const totalEpisodes = Math.floor(Math.random() * 50) + 10; // 10-60集
      const extendedDrama = {
        ...dramaData,
        totalEpisodes,
        rating: 4.2 + Math.random() * 0.6, // 4.2-4.8分
        releaseDate: '2024-01-15',
        tags: dramaData.category_schema.split('、'),
        currentEpisode: 1,
      };
      
      setDrama(extendedDrama);
      setEpisodes(generateMockEpisodes(totalEpisodes));
      setVideoQualities(generateMockQualities());
      
      // 获取相关推荐
      const related = await api.getRecommendedDramas(8);
      setRelatedDramas(related.filter(item => item.book_id !== dramaId));
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取短剧详情失败');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 处理集数选择
   */
  const handleEpisodeSelect = useCallback((episodeNumber: number) => {
    setCurrentEpisode(episodeNumber);
    if (drama) {
      addToHistory({
        ...drama,
        currentEpisode: episodeNumber,
      });
    }
  }, [drama, addToHistory]);

  /**
   * 处理播放
   */
  const handlePlay = () => {
    if (drama) {
      addToHistory({
        ...drama,
        currentEpisode,
      });
      setShowPlayer(true);
    }
  };

  /**
   * 处理收藏
   */
  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
  };

  /**
   * 处理分享
   */
  const handleShare = async () => {
    if (!drama) return;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: drama.title,
          text: drama.desc,
          url: window.location.href,
        });
      } catch (err) {
        console.log('分享取消');
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  /**
   * 返回上一页
   */
  const handleGoBack = () => {
    router.back();
  };

  /**
   * 初始化数据
   */
  useEffect(() => {
    if (id && typeof id === 'string') {
      fetchDramaDetail(id);
    }
  }, [id]);

  if (isLoading) {
    return (
      <SimpleLayout>
        <div className="min-h-screen flex items-center justify-center">
          <Loading size="lg" text="加载中..." />
        </div>
      </SimpleLayout>
    );
  }

  if (error || !drama) {
    return (
      <SimpleLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="text-6xl mb-4">😕</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {error || '短剧不存在'}
            </h1>
            <p className="text-gray-600 mb-6">
              抱歉，无法找到您要查看的短剧
            </p>
            <Button onClick={handleGoBack}>
              返回上一页
            </Button>
          </div>
        </div>
      </SimpleLayout>
    );
  }

  const watchHistory = getDramaHistory(drama.book_id);
  const isWatched = hasWatched(drama.book_id);

  return (
    <>
      <Head>
        <title>{drama.title} - 短剧搜索</title>
        <meta name="description" content={drama.desc} />
        <meta name="keywords" content={`${drama.title},${drama.author},${drama.category_schema},短剧`} />
        <meta property="og:title" content={drama.title} />
        <meta property="og:description" content={drama.desc} />
        <meta property="og:image" content={drama.cover} />
        <meta property="og:type" content="video.movie" />
      </Head>

      <SimpleLayout>
        <div className="min-h-screen bg-gray-50">
          {/* 头部导航 */}
          <div className="bg-white shadow-sm">
            <div className="container mx-auto px-4 py-4">
              <button
                onClick={handleGoBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                返回
              </button>
            </div>
          </div>

          <div className="container mx-auto px-4 py-6">
            {/* 主要内容区域 */}
            <div className="space-y-6">
              {/* 视频播放器区域 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-xl shadow-soft overflow-hidden"
              >
                {/* 播放器容器 */}
                <div className="relative aspect-video bg-black">
                  <VideoPlayer
                    drama={drama}
                    videoUrl={episodes[currentEpisode - 1]?.videoUrl}
                    onEnded={() => {}}
                    onError={(error) => console.error('播放错误:', error)}
                    className="w-full h-full"
                  />
                  
                  {/* 播放器覆盖层信息 */}
                  <div className="absolute top-4 left-4 z-10">
                    <div className="bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 text-white">
                      <div className="text-sm font-medium">{drama.title}</div>
                      <div className="text-xs text-gray-300">
                        第{currentEpisode}集 / 共{drama.totalEpisodes}集
                      </div>
                    </div>
                  </div>
                  
                  {/* 画质指示器 */}
                  <div className="absolute top-4 right-4 z-10">
                    <QualityIndicator quality={currentQuality} />
                  </div>
                </div>

                {/* 播放器控制区域 */}
                <div className="p-6 border-b border-gray-200">
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                    {/* 集数控制 */}
                    <EpisodeControls
                      currentEpisode={currentEpisode}
                      totalEpisodes={drama.totalEpisodes || 1}
                      onPreviousEpisode={() => {
                        if (currentEpisode > 1) {
                          handleEpisodeSelect(currentEpisode - 1);
                        }
                      }}
                      onNextEpisode={() => {
                        if (drama && currentEpisode < drama.totalEpisodes!) {
                          handleEpisodeSelect(currentEpisode + 1);
                        }
                      }}
                      onReplay={() => {
                        if (drama) {
                          addToHistory({
                            ...drama,
                            currentEpisode,
                          });
                        }
                      }}
                    />

                    {/* 画质选择 */}
                    <QualitySelector
                      qualities={videoQualities}
                      currentQuality={currentQuality}
                      onQualityChange={(quality: string) => {
                        setCurrentQuality(quality);
                      }}
                    />
                  </div>
                </div>
              </motion.div>

              {/* 剧集列表 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <EpisodeList
                  episodes={episodes}
                  currentEpisode={currentEpisode}
                  onEpisodeSelect={handleEpisodeSelect}
                />
              </motion.div>
            </div>
          </div>
        </div>
      </SimpleLayout>
    </>
  );
}
