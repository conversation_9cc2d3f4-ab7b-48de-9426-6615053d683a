import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { ArrowLeft, Play, SkipBack, SkipForward, Maximize } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Loading } from '@/components/ui/Loading';
import { SimpleLayout } from '@/components/layout/Layout';
import { api } from '@/lib/api';
import type { Drama } from '@/types';

/**
 * 短剧详情页面组件 - 简化版本
 */
export default function DramaDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const [drama, setDrama] = useState<Drama | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentEpisode, setCurrentEpisode] = useState(1);
  const [currentQuality, setCurrentQuality] = useState('720p');
  const [totalEpisodes, setTotalEpisodes] = useState(80);

  /**
   * 画质选项
   */
  const qualityOptions = [
    { label: '高清 720p', value: '720p' },
    { label: '超清 1080p', value: '1080p' },
    { label: '原始画质', value: 'original' },
  ];

  /**
   * 获取短剧详情 - 简化版本
   */
  const fetchDramaDetail = async (dramaId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // 使用模拟数据，避免API错误
      const mockDrama: Drama = {
        book_id: dramaId,
        title: '甜妻似火，霍爷他超爱',
        desc: '七年前，苏卿卿误入霍氏总裁霍玄夜房间，一夜纠缠后诞下双胞胎。大宝被苏宏瑞指使人偷走遗弃，只剩小宝苏小果相伴。七年后，苏卿卿携子回国寻子，与霍玄夜重逢却不相识，两人在多次接触中情愫暗生。',
        cover: '/api/placeholder/400/600',
        author: '霍玄夜,苏卿卿',
        category_schema: '萌宝·现代言情·80集',
        update_time: '2024-01-15',
      };

      setDrama(mockDrama);
      setTotalEpisodes(80);

    } catch (err) {
      setError('获取短剧详情失败');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 处理集数选择
   */
  const handleEpisodeSelect = (episodeNumber: number) => {
    setCurrentEpisode(episodeNumber);
  };

  /**
   * 处理画质选择
   */
  const handleQualityChange = (quality: string) => {
    setCurrentQuality(quality);
  };

  /**
   * 返回上一页
   */
  const handleGoBack = () => {
    router.back();
  };

  /**
   * 初始化数据
   */
  useEffect(() => {
    if (id && typeof id === 'string') {
      fetchDramaDetail(id);
    }
  }, [id]);

  if (isLoading) {
    return (
      <SimpleLayout>
        <div className="min-h-screen flex items-center justify-center">
          <Loading size="lg" text="加载中..." />
        </div>
      </SimpleLayout>
    );
  }

  if (error || !drama) {
    return (
      <SimpleLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="text-6xl mb-4">😕</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {error || '短剧不存在'}
            </h1>
            <p className="text-gray-600 mb-6">
              抱歉，无法找到您要查看的短剧
            </p>
            <Button onClick={handleGoBack}>
              返回上一页
            </Button>
          </div>
        </div>
      </SimpleLayout>
    );
  }

  return (
    <>
      <Head>
        <title>{drama.title} - 短剧搜索</title>
        <meta name="description" content={drama.desc} />
      </Head>

      <SimpleLayout>
        <div className="min-h-screen bg-gray-50">
          {/* 头部导航 */}
          <div className="bg-white shadow-sm">
            <div className="container mx-auto px-4 py-4">
              <button
                onClick={handleGoBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                返回
              </button>
            </div>
          </div>

          <div className="container mx-auto px-4 py-6 max-w-4xl">
            {/* 播放控制区域 */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="flex flex-wrap items-center gap-4 mb-4">
                {/* 集数控制按钮 */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => currentEpisode > 1 && handleEpisodeSelect(currentEpisode - 1)}
                    disabled={currentEpisode <= 1}
                    className="flex items-center gap-1"
                  >
                    <SkipBack className="w-4 h-4" />
                    上一集
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => currentEpisode < totalEpisodes && handleEpisodeSelect(currentEpisode + 1)}
                    disabled={currentEpisode >= totalEpisodes}
                    className="flex items-center gap-1"
                  >
                    下一集
                    <SkipForward className="w-4 h-4" />
                  </Button>

                  <Button
                    variant="primary"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Maximize className="w-4 h-4" />
                    全屏播放
                  </Button>
                </div>

                {/* 画质选择 */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">画质:</span>
                  <select
                    value={currentQuality}
                    onChange={(e) => handleQualityChange(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {qualityOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* 短剧信息 */}
              <div className="border-t pt-4">
                <h1 className="text-xl font-bold text-gray-900 mb-2">{drama.title}</h1>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">画质:</span> {qualityOptions.find(q => q.value === currentQuality)?.label}
                  </div>
                  <div>
                    <span className="font-medium">时长:</span> 约8分钟
                  </div>
                  <div>
                    <span className="font-medium">大小:</span> 约50MB
                  </div>
                  <div>
                    <span className="font-medium">当前:</span> 第{currentEpisode}集
                  </div>
                </div>
              </div>
            </div>

            {/* 剧集列表 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                剧集列表 (共{totalEpisodes}集)
              </h2>

              <div className="grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 gap-2">
                {Array.from({ length: totalEpisodes }, (_, index) => {
                  const episodeNumber = index + 1;
                  const isActive = episodeNumber === currentEpisode;

                  return (
                    <button
                      key={episodeNumber}
                      onClick={() => handleEpisodeSelect(episodeNumber)}
                      className={`
                        px-3 py-2 text-sm rounded transition-colors
                        ${isActive
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }
                      `}
                    >
                      {episodeNumber}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </SimpleLayout>
    </>
  );
}
